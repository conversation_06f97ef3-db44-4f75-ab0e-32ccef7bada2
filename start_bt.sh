#!/bin/bash

# live_room_service 宝塔面板启动脚本
# 适用于宝塔面板 Supervisor 进程管理

# 设置项目路径 (请根据实际部署路径修改)
PROJECT_PATH="/www/wwwroot/live_room_service"

# 切换到项目目录
cd $PROJECT_PATH

# 检查项目目录是否存在
if [ ! -d "$PROJECT_PATH" ]; then
    echo "❌ 项目目录不存在: $PROJECT_PATH"
    exit 1
fi

# 激活虚拟环境 (如果使用虚拟环境)
if [ -d "$PROJECT_PATH/venv" ]; then
    echo "✅ 激活虚拟环境..."
    source $PROJECT_PATH/venv/bin/activate
else
    echo "⚠️  未找到虚拟环境，使用系统Python"
fi

# 设置Python路径
export PYTHONPATH="$PROJECT_PATH/deploy/bin:$PROJECT_PATH/fqparty-py/src:$PROJECT_PATH/tomato-py/src:$PYTHONPATH"

# 创建日志目录
mkdir -p $PROJECT_PATH/logs

# 检查Redis服务
echo "🔍 检查Redis服务..."
if ! redis-cli ping > /dev/null 2>&1; then
    echo "❌ Redis服务未运行，请先启动Redis"
    exit 1
fi
echo "✅ Redis服务正常"

# 检查端口占用
echo "🔍 检查端口9200..."
if netstat -tlnp | grep :9200 > /dev/null 2>&1; then
    echo "⚠️  端口9200已被占用，正在尝试停止旧进程..."
    # 查找并杀死占用端口的进程
    PID=$(netstat -tlnp | grep :9200 | awk '{print $7}' | cut -d'/' -f1)
    if [ ! -z "$PID" ]; then
        kill -9 $PID
        sleep 2
    fi
fi

# 启动服务
echo "🚀 启动live_room_service..."
python3 start_production.py

# 检查启动结果
if [ $? -eq 0 ]; then
    echo "✅ 服务启动成功"
else
    echo "❌ 服务启动失败"
    exit 1
fi
