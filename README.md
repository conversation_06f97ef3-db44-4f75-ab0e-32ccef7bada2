# 直播间服务 - 简化版

## 📁 文件说明

### 🚀 启动服务文件
- **`start_all_services_final.py`** - 启动所有服务的主脚本
- **`start_websocket_service.py`** - 单独启动WebSocket连接服务
- **`start_single_service.py`** - 启动单个服务的通用脚本

### 🧪 测试文件
- **`test_websocket_connection.py`** - WebSocket连接测试脚本
- **`test_correct_format.py`** - 测试多种消息格式的脚本

### 🔧 配置文件
- **`setup_test_token.py`** - 设置测试token到Redis
- **`stackless.py`** - stackless模块兼容性补丁
- **`requirements.txt`** - Python依赖包列表

### 📂 核心目录
- **`deploy/`** - 服务部署目录（包含配置、日志、二进制文件）
- **`fqparty-py/`** - 业务逻辑源码
- **`tomato-py/`** - 框架源码

## 🚀 快速启动

### 1. 启动所有服务
```bash
python start_all_services_final.py
```

### 2. 单独启动WebSocket服务
```bash
python start_websocket_service.py
```

### 3. 设置测试token
```bash
python setup_test_token.py
```

### 4. 测试WebSocket连接
```bash
python test_websocket_connection.py
```

## 🌐 服务信息

### WebSocket连接
- **地址**: `ws://127.0.0.1:9200/`
- **域名**: `ws://dudupy.yuyan sound.com:9200/` (需配置hosts)

### 消息格式
```json
{
    "route": "/user/bindAndEnterRoom",
    "roomId": 100216,
    "authToken": "072d576456d38bfc41208c404fc3c087",
    "platform": "iOS,18.3",
    "version": "5.0.0",
    "isReceivePush": 1,
    "onMicForLogout": 1
}
```

### 支持的路由
- `/user/bind` - 用户绑定
- `/user/bindAndEnterRoom` - 用户绑定并进入房间

## 📋 系统要求

- Python 3.8+
- Redis服务器
- Windows系统

## 🔧 依赖安装

```bash
pip install -r requirements.txt
```

## 📝 注意事项

1. 确保Redis服务正在运行
2. 每个服务会在独立的控制台窗口中运行
3. 关闭控制台窗口可以停止对应的服务
4. 日志文件保存在 `deploy/logs/` 目录中

## 🎯 测试流程

1. 启动服务: `python start_all_services_final.py`
2. 设置token: `python setup_test_token.py`
3. 测试连接: `python test_websocket_connection.py`

## 🆘 故障排除

如果遇到问题，请检查：
1. Redis是否正在运行
2. 端口9200是否被占用
3. Python依赖是否完整安装
4. 日志文件中的错误信息
