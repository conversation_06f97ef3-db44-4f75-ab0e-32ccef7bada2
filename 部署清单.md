# live_room_service 部署清单

## 📦 **项目文件清单**

### 核心文件
- ✅ `deploy/` - 部署配置和核心代码
- ✅ `fqparty-py/` - 业务逻辑代码
- ✅ `tomato-py/` - 框架核心代码
- ✅ `requirements.txt` - Python依赖列表

### 启动脚本
- ✅ `start_production.py` - 生产环境启动脚本
- ✅ `start_bt.sh` - 宝塔面板启动脚本
- ✅ `start_all_services_final.py` - 完整服务启动脚本
- ✅ `start_websocket_service.py` - WebSocket服务启动脚本

### 配置文件
- ✅ `supervisor_config.conf` - Supervisor进程管理配置
- ✅ `quick_deploy.sh` - 快速部署脚本

### 文档
- ✅ `宝塔面板部署文档.md` - 详细部署文档
- ✅ `部署清单.md` - 本文件
- ✅ `README.md` - 项目说明

## 🗑️ **已清理的测试文件**

以下测试文件已被删除，保持项目整洁：
- ❌ `test_*.py` - 所有测试文件
- ❌ `setup_simple_test_token.py` - 测试token设置
- ❌ `check_all_services.py` - 服务检查脚本
- ❌ `websocket_server_patch.py` - 服务器补丁
- ❌ `virtual_client_success.py` - 虚拟客户端
- ❌ `__pycache__/` - Python缓存文件

## 🚀 **推荐部署方式**

### 方式1: 使用快速部署脚本 (推荐)
```bash
# 上传项目到服务器后执行
chmod +x quick_deploy.sh
./quick_deploy.sh
```

### 方式2: 使用Supervisor管理 (推荐生产环境)
```bash
# 1. 复制Supervisor配置
cp supervisor_config.conf /etc/supervisor/conf.d/live_room_service.conf

# 2. 重新加载配置
supervisorctl reread
supervisorctl update

# 3. 启动服务
supervisorctl start live_room_service
```

### 方式3: 直接运行 (开发测试)
```bash
# 设置权限
chmod +x start_production.py

# 启动服务
python3 start_production.py
```

## 📋 **部署前检查清单**

### 系统环境
- [ ] Linux服务器 (Ubuntu 18.04+ / CentOS 7+)
- [ ] Python 3.8+ 已安装
- [ ] pip3 已安装
- [ ] 宝塔面板已安装 (可选)

### 依赖服务
- [ ] Redis服务已安装并运行
- [ ] Supervisor已安装 (推荐)

### 网络配置
- [ ] 端口9200已开放 (WebSocket服务)
- [ ] 端口9000已开放 (HTTP服务，可选)
- [ ] 防火墙配置正确

### 文件权限
- [ ] 项目目录权限正确
- [ ] 启动脚本可执行权限
- [ ] 日志目录写入权限

## 🔧 **服务管理命令**

### Supervisor管理
```bash
# 查看状态
supervisorctl status live_room_service

# 启动服务
supervisorctl start live_room_service

# 停止服务
supervisorctl stop live_room_service

# 重启服务
supervisorctl restart live_room_service

# 查看日志
supervisorctl tail -f live_room_service
```

### 系统服务管理
```bash
# 查看端口监听
netstat -tlnp | grep :9200

# 查看进程
ps aux | grep python

# 查看日志
tail -f /www/wwwroot/live_room_service/logs/supervisor.log
```

## 🌐 **服务地址**

部署完成后，服务将在以下地址可用：

- **WebSocket服务**: `ws://your-server-ip:9200/`
- **HTTP服务**: `http://your-server-ip:9000/` (如果启用)

## 📊 **性能建议**

### 服务器配置
- **最低配置**: 2核CPU, 2GB内存, 10GB磁盘
- **推荐配置**: 4核CPU, 4GB内存, 20GB磁盘
- **高并发配置**: 8核CPU, 8GB内存, 50GB磁盘

### 优化建议
1. 使用SSD硬盘提升I/O性能
2. 配置Redis持久化
3. 设置日志轮转避免磁盘占满
4. 配置监控告警
5. 定期备份数据

## 🔒 **安全建议**

1. **防火墙配置**: 只开放必要端口
2. **用户权限**: 使用非root用户运行服务
3. **SSL证书**: 配置HTTPS/WSS加密
4. **访问控制**: 配置IP白名单
5. **定期更新**: 保持系统和依赖包最新

## 📞 **技术支持**

如果在部署过程中遇到问题，请检查：

1. **日志文件**: `/www/wwwroot/live_room_service/logs/`
2. **系统日志**: `/var/log/messages` 或 `/var/log/syslog`
3. **Redis日志**: `/var/log/redis/`
4. **Supervisor日志**: `/var/log/supervisor/`

常见问题解决方案请参考 `宝塔面板部署文档.md` 中的故障排除章节。
