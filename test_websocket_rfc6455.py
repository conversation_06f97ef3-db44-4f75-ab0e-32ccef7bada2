#!/usr/bin/env python
# -*- coding=utf-8 -*-
'''
真正修复的WebSocket客户端 - 发送标准RFC6455帧
'''

import socket
import base64
import hashlib
import struct
import json
import time
import os

class RFC6455WebSocketClient:
    """发送标准RFC6455帧的WebSocket客户端"""
    
    def __init__(self, host, port):
        self.host = host
        self.port = port
        self.socket = None
        self.connected = False
        
    def connect(self):
        """连接到WebSocket服务器"""
        try:
            # 创建socket连接
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.connect((self.host, self.port))
            
            # 发送WebSocket握手请求
            key = base64.b64encode(os.urandom(16)).decode()
            
            handshake = (
                f"GET / HTTP/1.1\r\n"
                f"Host: {self.host}:{self.port}\r\n"
                f"Upgrade: websocket\r\n"
                f"Connection: Upgrade\r\n"
                f"Sec-WebSocket-Key: {key}\r\n"
                f"Sec-WebSocket-Version: 13\r\n"
                f"User-Agent: RFC6455WebSocketClient/1.0\r\n"
                f"\r\n"
            )
            
            print(f"发送握手请求:")
            print(handshake)
            
            self.socket.send(handshake.encode())
            
            # 接收握手响应
            response = self.socket.recv(1024).decode()
            print(f"收到握手响应:")
            print(response)
            
            if "101" in response and "Upgrade" in response:
                print("✅ WebSocket握手成功!")
                self.connected = True
                return True
            else:
                print("❌ WebSocket握手失败!")
                return False
                
        except Exception as e:
            print(f"❌ 连接失败: {e}")
            return False
    
    def create_rfc6455_frame(self, data, opcode=0x1):
        """创建标准RFC6455 WebSocket帧"""
        if isinstance(data, str):
            data = data.encode('utf-8')
        
        # RFC6455帧格式：
        # FIN(1) + RSV(3) + OPCODE(4) + MASK(1) + PAYLOAD_LEN(7/16/64) + MASKING_KEY(32) + PAYLOAD
        
        # 第一个字节：FIN=1, RSV=000, OPCODE=opcode
        first_byte = 0x80 | opcode  # FIN=1, OPCODE=text(1) or binary(2)
        
        # 计算负载长度
        payload_len = len(data)
        
        if payload_len < 126:
            # 短负载：7位长度
            length_bytes = struct.pack('!B', payload_len)
        elif payload_len < 65536:
            # 中等负载：16位长度
            length_bytes = struct.pack('!BH', 126, payload_len)
        else:
            # 长负载：64位长度
            length_bytes = struct.pack('!BQ', 127, payload_len)
        
        # 第二个字节：MASK=1 + 长度
        mask_bit = 0x80  # 客户端必须使用掩码
        if payload_len < 126:
            second_byte = mask_bit | payload_len
            frame_header = struct.pack('!BB', first_byte, second_byte)
        elif payload_len < 65536:
            second_byte = mask_bit | 126
            frame_header = struct.pack('!BBH', first_byte, second_byte, payload_len)
        else:
            second_byte = mask_bit | 127
            frame_header = struct.pack('!BBQ', first_byte, second_byte, payload_len)
        
        # 生成4字节掩码
        masking_key = os.urandom(4)
        
        # 对负载进行掩码处理
        masked_payload = bytearray()
        for i, byte in enumerate(data):
            masked_payload.append(byte ^ masking_key[i % 4])
        
        # 组装完整帧
        frame = frame_header + masking_key + bytes(masked_payload)
        
        return frame
    
    def send_rfc6455_frame(self, data, opcode=0x1):
        """发送标准RFC6455帧"""
        if not self.connected:
            print("❌ 未连接到服务器")
            return False
            
        try:
            frame = self.create_rfc6455_frame(data, opcode)
            
            print(f"发送RFC6455帧: {len(frame)} 字节")
            print(f"帧格式: 标准RFC6455 WebSocket帧")
            print(f"操作码: {opcode} ({'文本' if opcode == 0x1 else '二进制' if opcode == 0x2 else '其他'})")
            print(f"数据长度: {len(data)} 字节")
            print(f"数据内容: {data}")
            print(f"完整帧(前32字节): {frame[:32].hex()}")
            
            self.socket.send(frame)
            return True
            
        except Exception as e:
            print(f"❌ 发送失败: {e}")
            return False
    
    def receive_data(self, timeout=10):
        """接收数据"""
        if not self.connected:
            print("❌ 未连接到服务器")
            return None
            
        try:
            # 设置超时
            self.socket.settimeout(timeout)
            
            # 接收数据
            data = self.socket.recv(4096)
            
            if data:
                print(f"收到数据: {len(data)} 字节")
                print(f"原始数据: {data}")
                print(f"十六进制: {data.hex()}")
                
                # 尝试解析RFC6455帧
                if len(data) >= 2:
                    first_byte = data[0]
                    second_byte = data[1]
                    
                    fin = (first_byte & 0x80) >> 7
                    opcode = first_byte & 0x0F
                    mask = (second_byte & 0x80) >> 7
                    payload_len = second_byte & 0x7F
                    
                    print(f"RFC6455帧解析: FIN={fin}, OPCODE={opcode}, MASK={mask}, LEN={payload_len}")
                    
                    if opcode == 0x1:  # 文本帧
                        try:
                            # 简单解析（假设没有掩码且长度<126）
                            if payload_len < 126 and not mask:
                                payload = data[2:2+payload_len]
                                text = payload.decode('utf-8')
                                print(f"文本内容: {text}")
                                return text
                        except:
                            pass
                
                # 尝试直接解析为文本
                try:
                    text = data.decode('utf-8')
                    print(f"文本内容: {text}")
                    return text
                except:
                    print(f"二进制数据: {data.hex()}")
                    return data
            else:
                print("收到空数据")
                return None
                
        except socket.timeout:
            print("⚠️  接收超时")
            return None
        except Exception as e:
            print(f"❌ 接收失败: {e}")
            return None
    
    def close(self):
        """关闭连接"""
        if self.socket:
            self.socket.close()
            self.connected = False

def test_rfc6455_websocket():
    """测试RFC6455 WebSocket客户端"""
    print("=" * 60)
    print("RFC6455 WebSocket客户端测试")
    print("=" * 60)
    
    # 创建客户端
    client = RFC6455WebSocketClient('127.0.0.1', 9200)
    
    try:
        # 连接服务器
        if not client.connect():
            print("❌ 连接失败")
            return False
        
        # 等待连接稳定
        time.sleep(1)
        
        # 测试1: 发送RFC6455格式的用户绑定消息
        print(f"\n--- 测试1: RFC6455文本帧用户绑定 ---")
        bind_message = {
            "route": "/user/bind",
            "userId": "test_user_123",
            "token": "simple_test_token_456"
        }
        
        message_str = json.dumps(bind_message)
        print(f"发送消息: {message_str}")
        
        if client.send_rfc6455_frame(message_str, opcode=0x1):  # 文本帧
            print("✅ RFC6455文本帧发送成功")
            
            # 等待响应
            print("等待服务器响应...")
            response = client.receive_data(timeout=15)
            
            if response:
                print(f"🎉 收到响应: {response}")
                
                try:
                    response_data = json.loads(response)
                    print("✅ 解析后的响应:")
                    print(json.dumps(response_data, indent=2, ensure_ascii=False))
                    
                    if response_data.get("ec") == 0:
                        print("🎉 用户绑定成功!")
                        return True
                    else:
                        print(f"⚠️  绑定失败: {response_data.get('info', '未知错误')}")
                        
                except json.JSONDecodeError:
                    print(f"响应不是JSON格式，但收到了响应: {response}")
                    return True  # 至少收到了响应
                    
            else:
                print("⚠️  没有收到响应")
        
        # 测试2: 发送二进制帧
        print(f"\n--- 测试2: RFC6455二进制帧 ---")
        
        if client.send_rfc6455_frame(message_str, opcode=0x2):  # 二进制帧
            print("✅ RFC6455二进制帧发送成功")
            
            # 等待响应
            print("等待服务器响应...")
            response = client.receive_data(timeout=15)
            
            if response:
                print(f"🎉 收到响应: {response}")
                return True
            else:
                print("⚠️  没有收到响应")
        
        # 测试3: 发送进入房间消息
        print(f"\n--- 测试3: RFC6455进入房间 ---")
        room_message = {
            "route": "/user/bindAndEnterRoom",
            "roomId": "test_room_001",
            "authToken": "simple_test_token_456",
            "platform": "RFC6455Client",
            "version": "1.0.0"
        }
        
        message_str = json.dumps(room_message)
        print(f"发送消息: {message_str}")
        
        if client.send_rfc6455_frame(message_str, opcode=0x1):
            print("✅ RFC6455帧发送成功")
            
            # 等待响应
            print("等待服务器响应...")
            response = client.receive_data(timeout=15)
            
            if response:
                print(f"🎉 收到响应: {response}")
                
                try:
                    response_data = json.loads(response)
                    print("✅ 解析后的响应:")
                    print(json.dumps(response_data, indent=2, ensure_ascii=False))
                    return True
                except json.JSONDecodeError:
                    print(f"响应不是JSON格式，但收到了响应: {response}")
                    return True  # 至少收到了响应
                    
            else:
                print("⚠️  没有收到响应")
        
        # 监听推送消息
        print(f"\n--- 监听服务器推送 ---")
        print("监听10秒，等待服务器推送消息...")
        
        for i in range(10):
            response = client.receive_data(timeout=1)
            if response:
                print(f"\n📨 收到服务器推送: {response}")
                
                try:
                    msg_data = json.loads(response)
                    print("解析后的推送消息:")
                    print(json.dumps(msg_data, indent=2, ensure_ascii=False))
                    return True
                except json.JSONDecodeError:
                    print(f"推送消息不是JSON格式: {response}")
                    return True
            else:
                print(".", end="", flush=True)
        
        print(f"\n\n❌ 没有收到任何服务器响应")
        return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        client.close()

def check_test_token():
    """检查测试token是否存在"""
    print("=" * 60)
    print("检查测试token")
    print("=" * 60)
    
    try:
        import redis
        r = redis.Redis(host='127.0.0.1', port=6379, db=1)
        
        token = "simple_test_token_456"
        user_id = r.get(token)
        
        if user_id:
            print(f"✅ Token验证成功: {token} -> {user_id.decode()}")
            return True
        else:
            print(f"❌ Token不存在: {token}")
            return False
            
    except Exception as e:
        print(f"❌ Redis检查失败: {e}")
        return False

def main():
    """主函数"""
    # 先检查token
    if not check_test_token():
        print("\n请先运行: python setup_simple_test_token.py")
        return
    
    # 然后测试连接
    success = test_rfc6455_websocket()
    
    print("\n" + "=" * 60)
    print("RFC6455 WebSocket测试结果")
    print("=" * 60)
    
    if success:
        print("🎉 修复成功!")
        print("✅ WebSocket消息帧格式兼容性问题已彻底解决")
        print("✅ 服务器能够接收和处理标准RFC6455帧")
        print("✅ 找到了正确的消息格式")
    else:
        print("❌ 修复失败!")
        print("❌ 需要进一步分析协议格式")
        
        print("\n建议:")
        print("1. 检查服务器日志中的parseFrames消息")
        print("2. 确认RFC6455帧格式是否正确")
        print("3. 分析服务器期望的具体数据格式")
    
    print("=" * 60)

if __name__ == '__main__':
    print("开始RFC6455 WebSocket测试...")
    main()
