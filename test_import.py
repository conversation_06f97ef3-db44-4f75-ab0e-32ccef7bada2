#!/usr/bin/env python
# -*- coding=utf-8 -*-
'''
测试模块导入的脚本
'''

import os
import sys

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
bin_dir = os.path.join(current_dir, 'deploy', 'bin')
sys.path.append(bin_dir)

print("Python路径:")
for i, path in enumerate(sys.path):
    print(f"  {i}: {path}")

print("\n开始测试导入...")

try:
    print("1. 测试标准库http模块...")
    import http.cookiejar
    print("   ✅ 标准库http模块导入成功")
except Exception as e:
    print(f"   ❌ 标准库http模块导入失败: {e}")

try:
    print("2. 测试stackless模块...")
    import stackless
    print("   ✅ stackless模块导入成功")
    print(f"   - bomb类: {hasattr(stackless, 'bomb')}")
    print(f"   - tasklet类: {hasattr(stackless, 'tasklet')}")
    print(f"   - channel类: {hasattr(stackless, 'channel')}")
except Exception as e:
    print(f"   ❌ stackless模块导入失败: {e}")

try:
    print("3. 测试tomato模块...")
    import tomato
    print("   ✅ tomato模块导入成功")
except Exception as e:
    print(f"   ❌ tomato模块导入失败: {e}")

try:
    print("4. 测试tomato.server模块...")
    from tomato.server import server
    print("   ✅ tomato.server模块导入成功")
except Exception as e:
    print(f"   ❌ tomato.server模块导入失败: {e}")
    import traceback
    traceback.print_exc()

try:
    print("5. 测试fqparty模块...")
    import fqparty
    print("   ✅ fqparty模块导入成功")
    print(f"   - app属性: {hasattr(fqparty, 'app')}")
except Exception as e:
    print(f"   ❌ fqparty模块导入失败: {e}")
    import traceback
    traceback.print_exc()

print("\n测试完成！")
