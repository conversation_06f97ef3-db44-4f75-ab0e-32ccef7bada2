#!/usr/bin/env python
# -*- coding=utf-8 -*-
'''
WebSocket服务器修复补丁
修复wsold协议与现代WebSocket客户端的兼容性问题
'''

import os
import sys
import shutil
from pathlib import Path

def backup_original_file(file_path):
    """备份原始文件"""
    backup_path = f"{file_path}.backup"
    if not os.path.exists(backup_path):
        shutil.copy2(file_path, backup_path)
        print(f"✅ 已备份原始文件: {backup_path}")
    else:
        print(f"⚠️  备份文件已存在: {backup_path}")

def patch_websocket_protocol():
    """修复WebSocket协议处理"""
    
    # 查找txws.py文件
    possible_paths = [
        "deploy/bin/tomato/utils/txws.py",
        "tomato-py/src/tomato/utils/txws.py"
    ]
    
    txws_path = None
    for path in possible_paths:
        if os.path.exists(path):
            txws_path = path
            break
    
    if not txws_path:
        print("❌ 找不到txws.py文件")
        return False
    
    print(f"📁 找到txws.py文件: {txws_path}")
    
    # 备份原始文件
    backup_original_file(txws_path)
    
    # 读取原始文件
    with open(txws_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 查找dataReceived方法
    if 'def dataReceived(self, data):' not in content:
        print("❌ 找不到dataReceived方法")
        return False
    
    # 添加调试日志到dataReceived方法
    old_data_received = '''    def dataReceived(self, data):
        self.buf += data

        if ttlog.isDebugEnabled():
            ttlog.debug('WebSocketProtocol.dataReceived',
                        'dataLen=', len(data),
                        'clientIp=', self.clientIp)'''
    
    new_data_received = '''    def dataReceived(self, data):
        self.buf += data

        if ttlog.isDebugEnabled():
            ttlog.debug('WebSocketProtocol.dataReceived',
                        'dataLen=', len(data),
                        'bufLen=', len(self.buf),
                        'clientIp=', self.clientIp,
                        'data=', data.hex() if len(data) < 200 else data[:100].hex() + '...')'''
    
    # 替换dataReceived方法
    if old_data_received in content:
        content = content.replace(old_data_received, new_data_received)
        print("✅ 已添加dataReceived调试日志")
    else:
        print("⚠️  dataReceived方法格式不匹配，跳过修改")
    
    # 查找parseFrames方法并添加更详细的日志
    old_parse_frames = '''        if ttlog.isDebugEnabled():
            ttlog.debug('WebSocketProtocol.parseFrames',
                        'dataLen=', len(data),
                        'clientIp=', self.clientIp,
                        'data=', self.printBytes(data))'''
    
    new_parse_frames = '''        if ttlog.isDebugEnabled():
            ttlog.debug('WebSocketProtocol.parseFrames',
                        'dataLen=', len(data),
                        'clientIp=', self.clientIp,
                        'opcode=', opcode,
                        'data=', self.printBytes(data))'''
    
    if old_parse_frames in content:
        content = content.replace(old_parse_frames, new_parse_frames)
        print("✅ 已添加parseFrames调试日志")
    
    # 添加强制刷新缓冲区的代码
    old_frames_handling = '''            elif self.state == FRAMES:
                if not self.callOpen:
                    self.callOpen = True
                    try:
                        self.composer = self.factory.composer()
                        self.factory.onConnectionOpen(self)
                    except:
                        self.abort()
                if ttlog.isDebugEnabled():
                    ttlog.debug('WebSocketProtocol.dataReceived FRAMES',
                                'bufLen=', len(self.buf),
                                'clientIp=', self.clientIp,
                                'calling parseFrames...')
                self.parseFrames()'''
    
    new_frames_handling = '''            elif self.state == FRAMES:
                if not self.callOpen:
                    self.callOpen = True
                    try:
                        self.composer = self.factory.composer()
                        self.factory.onConnectionOpen(self)
                    except:
                        self.abort()
                if ttlog.isDebugEnabled():
                    ttlog.debug('WebSocketProtocol.dataReceived FRAMES',
                                'bufLen=', len(self.buf),
                                'clientIp=', self.clientIp,
                                'bufContent=', self.buf.hex() if len(self.buf) < 200 else self.buf[:100].hex() + '...',
                                'calling parseFrames...')
                self.parseFrames()
                
                # 强制处理缓冲区中的所有数据
                if len(self.buf) > 0:
                    if ttlog.isDebugEnabled():
                        ttlog.debug('WebSocketProtocol.dataReceived',
                                    'forcing buffer processing',
                                    'bufLen=', len(self.buf),
                                    'clientIp=', self.clientIp)'''
    
    if old_frames_handling in content:
        content = content.replace(old_frames_handling, new_frames_handling)
        print("✅ 已添加强制缓冲区处理")
    
    # 写入修改后的文件
    with open(txws_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"✅ 已修复WebSocket协议文件: {txws_path}")
    return True

def create_enhanced_websocket_client():
    """创建增强的WebSocket客户端用于测试修复效果"""
    
    client_code = '''#!/usr/bin/env python
# -*- coding=utf-8 -*-
"""
增强的WebSocket客户端 - 测试修复效果
"""

import socket
import base64
import struct
import json
import time
import os

class EnhancedWebSocketClient:
    """增强的WebSocket客户端"""
    
    def __init__(self, host, port):
        self.host = host
        self.port = port
        self.socket = None
        self.connected = False
        
    def connect(self):
        """连接到WebSocket服务器"""
        try:
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.connect((self.host, self.port))
            
            # 发送WebSocket握手
            key = base64.b64encode(os.urandom(16)).decode()
            handshake = (
                f"GET / HTTP/1.1\\r\\n"
                f"Host: {self.host}:{self.port}\\r\\n"
                f"Upgrade: websocket\\r\\n"
                f"Connection: Upgrade\\r\\n"
                f"Sec-WebSocket-Key: {key}\\r\\n"
                f"Sec-WebSocket-Version: 13\\r\\n"
                f"User-Agent: EnhancedWebSocketClient/1.0\\r\\n"
                f"\\r\\n"
            )
            
            print(f"📤 发送握手请求")
            self.socket.send(handshake.encode())
            
            # 接收握手响应
            response = self.socket.recv(1024).decode()
            print(f"📥 收到握手响应")
            
            if "101" in response:
                print("✅ WebSocket握手成功!")
                self.connected = True
                return True
            else:
                print("❌ WebSocket握手失败!")
                return False
                
        except Exception as e:
            print(f"❌ 连接失败: {e}")
            return False
    
    def send_message(self, message):
        """发送WebSocket消息"""
        if not self.connected:
            return False
            
        try:
            # 创建RFC6455帧
            if isinstance(message, str):
                data = message.encode('utf-8')
            else:
                data = message
            
            # 简单的文本帧格式
            frame = bytearray()
            frame.append(0x81)  # FIN=1, OPCODE=1 (text)
            
            if len(data) < 126:
                frame.append(0x80 | len(data))  # MASK=1, LEN=data_len
            else:
                frame.append(0x80 | 126)  # MASK=1, LEN=126
                frame.extend(struct.pack('!H', len(data)))
            
            # 添加掩码
            mask = os.urandom(4)
            frame.extend(mask)
            
            # 掩码处理数据
            for i, byte in enumerate(data):
                frame.append(byte ^ mask[i % 4])
            
            print(f"📤 发送消息: {len(frame)} 字节")
            print(f"   内容: {message}")
            
            # 发送帧
            sent = self.socket.send(bytes(frame))
            print(f"✅ 发送完成: {sent} 字节")
            
            # 等待一下确保数据发送
            time.sleep(0.1)
            
            return True
            
        except Exception as e:
            print(f"❌ 发送失败: {e}")
            return False
    
    def receive_message(self, timeout=10):
        """接收WebSocket消息"""
        if not self.connected:
            return None
            
        try:
            self.socket.settimeout(timeout)
            data = self.socket.recv(4096)
            
            if data:
                print(f"📥 收到数据: {len(data)} 字节")
                print(f"   十六进制: {data.hex()}")
                
                try:
                    text = data.decode('utf-8')
                    print(f"   文本内容: {text}")
                    return text
                except:
                    return data
            else:
                return None
                
        except socket.timeout:
            print("⏰ 接收超时")
            return None
        except Exception as e:
            print(f"❌ 接收失败: {e}")
            return None
    
    def close(self):
        """关闭连接"""
        if self.socket:
            self.socket.close()
            self.connected = False

def test_enhanced_client():
    """测试增强客户端"""
    print("=" * 60)
    print("增强WebSocket客户端测试")
    print("=" * 60)
    
    client = EnhancedWebSocketClient('127.0.0.1', 9200)
    
    try:
        if not client.connect():
            return False
        
        time.sleep(1)
        
        # 发送用户绑定消息
        message = {
            "route": "/user/bind",
            "userId": "test_user_123",
            "token": "simple_test_token_456"
        }
        
        message_str = json.dumps(message)
        
        if client.send_message(message_str):
            print("\\n📥 等待服务器响应...")
            response = client.receive_message(timeout=15)
            
            if response:
                print(f"🎉 收到响应: {response}")
                return True
            else:
                print("❌ 没有收到响应")
                return False
        else:
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False
    finally:
        client.close()

if __name__ == '__main__':
    test_enhanced_client()
'''
    
    with open('test_enhanced_websocket.py', 'w', encoding='utf-8') as f:
        f.write(client_code)
    
    print("✅ 已创建增强WebSocket客户端: test_enhanced_websocket.py")

def main():
    """主函数"""
    print("=" * 60)
    print("WebSocket服务器修复补丁")
    print("=" * 60)
    
    print("🔧 开始修复WebSocket协议兼容性问题...")
    
    # 修复WebSocket协议
    if patch_websocket_protocol():
        print("✅ WebSocket协议修复完成")
    else:
        print("❌ WebSocket协议修复失败")
        return
    
    # 创建测试客户端
    create_enhanced_websocket_client()
    
    print("\\n" + "=" * 60)
    print("修复完成!")
    print("=" * 60)
    print("📋 修复内容:")
    print("1. ✅ 添加了详细的WebSocket数据接收日志")
    print("2. ✅ 添加了缓冲区内容的十六进制输出")
    print("3. ✅ 添加了强制缓冲区处理机制")
    print("4. ✅ 创建了增强的测试客户端")
    
    print("\\n🚀 下一步:")
    print("1. 重启WebSocket服务器")
    print("2. 运行: python test_enhanced_websocket.py")
    print("3. 查看服务器日志中的详细调试信息")
    
    print("\\n⚠️  注意:")
    print("- 原始文件已备份为 .backup")
    print("- 如需恢复，请将备份文件重命名回原文件名")

if __name__ == '__main__':
    main()
'''

