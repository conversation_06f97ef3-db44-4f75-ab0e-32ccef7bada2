#!/usr/bin/env python
# -*- coding=utf-8 -*-
'''
简化版服务启动脚本，绕过复杂的Redis连接
'''

import os
import sys
import time

def main():
    if len(sys.argv) < 2:
        print("用法: python simple_start_service.py <服务ID>")
        return
    
    service_id = sys.argv[1]
    
    # 项目路径配置
    current_dir = os.path.dirname(os.path.abspath(__file__))
    bin_dir = os.path.join(current_dir, 'deploy', 'bin')
    config_path = os.path.join(current_dir, 'deploy', 'config')
    logs_path = os.path.join(current_dir, 'deploy', 'logs')
    
    # 确保日志目录存在
    os.makedirs(logs_path, exist_ok=True)
    
    # 设置环境变量
    sys.path.insert(0, bin_dir)
    
    print(f"简化启动服务 {service_id}...")
    print("=" * 50)
    
    try:
        print("1. 导入基础模块...")
        import stackless
        import tomato
        from tomato.config import configure
        from tomato.utils import ttlog
        print("   ✅ 基础模块导入成功")
        
        print("2. 初始化配置...")
        configure.initByFile(config_path)
        print("   ✅ 配置初始化成功")
        
        print("3. 设置日志级别...")
        logLevel = configure.loadJson('server.tomato.global', {}).get('log', {}).get('level', ttlog.INFO)
        ttlog.setLevel(logLevel)
        print(f"   ✅ 日志级别设置为: {logLevel}")
        
        print("4. 初始化tomato应用...")
        tomato.app.appArgs = []
        tomato.app.init(service_id)
        print("   ✅ tomato应用初始化成功")
        
        # 跳过fqparty应用初始化，直接启动tomato应用
        print("5. 启动tomato应用...")
        tomato.app.start()
        print("   ✅ tomato应用启动成功")
        
        print("\n" + "=" * 50)
        print(f"🎉 服务 {service_id} 启动成功！")
        print("=" * 50)
        
        # 显示服务信息
        if service_id == 'HT000001':
            print("HTTP服务: http://127.0.0.1:9000/")
        elif service_id == 'CO000001':
            print("WebSocket服务: ws://127.0.0.1:9200/")
        
        print("按 Ctrl+C 停止服务")
        print("=" * 50)
        
        print("6. 启动主循环...")
        from tomato.core import reactor
        print("   ✅ 开始运行reactor...")
        
        # 简单的主循环
        try:
            while True:
                time.sleep(1)
                print(".", end="", flush=True)
        except KeyboardInterrupt:
            print("\n   ⚠️  收到停止信号")
        
    except KeyboardInterrupt:
        print(f"\n服务 {service_id} 已停止")
    except Exception as e:
        print(f"\n❌ 启动服务 {service_id} 失败!")
        print(f"错误类型: {type(e).__name__}")
        print(f"错误信息: {str(e)}")
        print("\n详细错误信息:")
        print("-" * 50)
        import traceback
        traceback.print_exc()
        print("-" * 50)

if __name__ == '__main__':
    main()
