#!/usr/bin/env python
# -*- coding=utf-8 -*-
'''
测试wsold协议的客户端
'''

import socket
import base64
import hashlib
import struct
import json
import time
import threading

class WSoldClient:
    """兼容wsold协议的WebSocket客户端"""
    
    def __init__(self, host, port):
        self.host = host
        self.port = port
        self.socket = None
        self.connected = False
        
    def connect(self):
        """连接到wsold服务器"""
        try:
            # 创建socket连接
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.connect((self.host, self.port))
            
            # 发送WebSocket握手请求
            key = base64.b64encode(b'wsold_test_key_123').decode()
            
            handshake = (
                f"GET / HTTP/1.1\r\n"
                f"Host: {self.host}:{self.port}\r\n"
                f"Upgrade: websocket\r\n"
                f"Connection: Upgrade\r\n"
                f"Sec-WebSocket-Key: {key}\r\n"
                f"Sec-WebSocket-Version: 13\r\n"
                f"User-Agent: WSoldClient/1.0\r\n"
                f"\r\n"
            )
            
            print(f"发送握手请求:")
            print(handshake)
            
            self.socket.send(handshake.encode())
            
            # 接收握手响应
            response = self.socket.recv(1024).decode()
            print(f"收到握手响应:")
            print(response)
            
            if "101" in response and "Upgrade" in response:
                print("✅ WebSocket握手成功!")
                self.connected = True
                return True
            else:
                print("❌ WebSocket握手失败!")
                return False
                
        except Exception as e:
            print(f"❌ 连接失败: {e}")
            return False
    
    def send_raw_message(self, data):
        """发送原始消息（不使用WebSocket帧格式）"""
        if not self.connected:
            print("❌ 未连接到服务器")
            return False
            
        try:
            # 直接发送原始数据，不使用WebSocket帧格式
            if isinstance(data, str):
                data = data.encode('utf-8')
            
            print(f"发送原始消息: {len(data)} 字节")
            print(f"消息内容: {data}")
            
            self.socket.send(data)
            return True
            
        except Exception as e:
            print(f"❌ 发送失败: {e}")
            return False
    
    def send_simple_frame(self, data):
        """发送简单帧格式（类似HyBi-00）"""
        if not self.connected:
            print("❌ 未连接到服务器")
            return False
            
        try:
            # 使用简单的帧格式：\x00 + data + \xff
            if isinstance(data, str):
                data = data.encode('utf-8')
            
            frame = b'\x00' + data + b'\xff'
            
            print(f"发送简单帧: {len(frame)} 字节")
            print(f"帧格式: \\x00 + data + \\xff")
            print(f"数据内容: {data}")
            
            self.socket.send(frame)
            return True
            
        except Exception as e:
            print(f"❌ 发送失败: {e}")
            return False
    
    def send_length_prefixed(self, data):
        """发送长度前缀格式"""
        if not self.connected:
            print("❌ 未连接到服务器")
            return False
            
        try:
            # 使用长度前缀格式：4字节长度 + 数据
            if isinstance(data, str):
                data = data.encode('utf-8')
            
            length = len(data)
            frame = struct.pack('!I', length) + data
            
            print(f"发送长度前缀消息: {len(frame)} 字节")
            print(f"格式: 4字节长度({length}) + 数据")
            print(f"数据内容: {data}")
            
            self.socket.send(frame)
            return True
            
        except Exception as e:
            print(f"❌ 发送失败: {e}")
            return False
    
    def receive_data(self, timeout=10):
        """接收数据"""
        if not self.connected:
            print("❌ 未连接到服务器")
            return None
            
        try:
            # 设置超时
            self.socket.settimeout(timeout)
            
            # 接收数据
            data = self.socket.recv(4096)
            
            if data:
                print(f"收到数据: {len(data)} 字节")
                print(f"原始数据: {data}")
                
                try:
                    # 尝试解析为文本
                    text = data.decode('utf-8')
                    print(f"文本内容: {text}")
                    return text
                except:
                    print(f"二进制数据: {data.hex()}")
                    return data
            else:
                print("收到空数据")
                return None
                
        except socket.timeout:
            print("⚠️  接收超时")
            return None
        except Exception as e:
            print(f"❌ 接收失败: {e}")
            return None
    
    def close(self):
        """关闭连接"""
        if self.socket:
            self.socket.close()
            self.connected = False

def test_wsold_protocol():
    """测试wsold协议"""
    print("=" * 60)
    print("WSold协议测试")
    print("=" * 60)
    
    # 创建客户端
    client = WSoldClient('127.0.0.1', 9200)
    
    try:
        # 连接服务器
        if not client.connect():
            print("❌ 连接失败")
            return False
        
        # 等待连接稳定
        time.sleep(1)
        
        # 测试1: 发送原始JSON消息
        print(f"\n--- 测试1: 原始JSON消息 ---")
        test_message = {
            "route": "/user/bind",
            "userId": "test_user_123",
            "token": "simple_test_token_456"
        }
        
        message_str = json.dumps(test_message)
        print(f"发送: {message_str}")
        
        if client.send_raw_message(message_str):
            print("✅ 消息发送成功")
            
            # 等待响应
            print("等待服务器响应...")
            response = client.receive_data(timeout=15)
            
            if response:
                print(f"🎉 收到响应: {response}")
                return True
            else:
                print("⚠️  没有收到响应")
        
        # 测试2: 发送简单帧格式
        print(f"\n--- 测试2: 简单帧格式 ---")
        
        if client.send_simple_frame(message_str):
            print("✅ 简单帧发送成功")
            
            # 等待响应
            print("等待服务器响应...")
            response = client.receive_data(timeout=15)
            
            if response:
                print(f"🎉 收到响应: {response}")
                return True
            else:
                print("⚠️  没有收到响应")
        
        # 测试3: 发送长度前缀格式
        print(f"\n--- 测试3: 长度前缀格式 ---")
        
        if client.send_length_prefixed(message_str):
            print("✅ 长度前缀消息发送成功")
            
            # 等待响应
            print("等待服务器响应...")
            response = client.receive_data(timeout=15)
            
            if response:
                print(f"🎉 收到响应: {response}")
                return True
            else:
                print("⚠️  没有收到响应")
        
        # 监听推送消息
        print(f"\n--- 监听服务器推送 ---")
        print("监听10秒，等待服务器推送消息...")
        
        for i in range(10):
            response = client.receive_data(timeout=1)
            if response:
                print(f"\n📨 收到服务器推送: {response}")
                return True
            else:
                print(".", end="", flush=True)
        
        print(f"\n\n❌ 没有收到任何服务器响应")
        return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        client.close()

def check_test_token():
    """检查测试token是否存在"""
    print("=" * 60)
    print("检查测试token")
    print("=" * 60)
    
    try:
        import redis
        r = redis.Redis(host='127.0.0.1', port=6379, db=1)
        
        token = "simple_test_token_456"
        user_id = r.get(token)
        
        if user_id:
            print(f"✅ Token验证成功: {token} -> {user_id.decode()}")
            return True
        else:
            print(f"❌ Token不存在: {token}")
            return False
            
    except Exception as e:
        print(f"❌ Redis检查失败: {e}")
        return False

def main():
    """主函数"""
    # 先检查token
    if not check_test_token():
        print("\n请先运行: python setup_simple_test_token.py")
        return
    
    # 然后测试连接
    success = test_wsold_protocol()
    
    print("\n" + "=" * 60)
    print("WSold协议测试结果")
    print("=" * 60)
    
    if success:
        print("🎉 WSold协议测试成功!")
        print("✅ 服务器能够接收和处理消息")
        print("✅ 找到了正确的消息格式")
    else:
        print("❌ WSold协议测试失败!")
        print("❌ 需要进一步分析协议格式")
        
        print("\n建议:")
        print("1. 检查服务器日志中的dataReceived消息")
        print("2. 分析TTComposerFake的具体实现")
        print("3. 查看MessageCodecOld的消息格式")
    
    print("=" * 60)

if __name__ == '__main__':
    print("开始WSold协议测试...")
    main()
