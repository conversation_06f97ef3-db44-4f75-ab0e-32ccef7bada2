"""

For ongoing work, see the GitHub contributors list at:
  https://github.com/deldotdr/txRedis/contributors

<AUTHOR> (<EMAIL>)
@date 12/21/11
Reorganized codebase.

<AUTHOR> (<EMAIL>)
@date 06/22/10
Added multi-bulk command sending support.
Added support for hash commands.
Added support for sorted set.
Added support for new basic commands APPEND and SUBSTR.
Removed forcing of float data to be decimal.
Removed inlineCallbacks within protocol code.
Added setuptools support to setup.py

<AUTHOR> (<EMAIL>)
@date 06/15/10
Added read buffering for bulk data.
Removed use of LineReceiver to avoid Twisted recursion bug.
Added support for multi, exec, and discard

<AUTHOR>
@date 02/01/10
Added BLPOP/BRPOP and RPOPLPUSH to list commands.
Added doc strings to list commands (copied from the Redis google code
project page).

<AUTHOR>
<AUTHOR>
@date 9/30/09
@brief Twisted compatible version of redis.py
"""
# for backwards compatibility
from .client import *
from .exceptions import *
from .protocol import *
