upstream goapi {
    server 127.0.0.1:7070;
}

server {
    listen       443 ssl;
    listen       [::]:443 ssl;
    server_name testgo.fqparty.com;
    access_log  /var/log/gochat.access.log;
    error_log  /var/log/gochat.error.log;

        ssl on;
        ssl_certificate /etc/nginx/zhengshu/fullchain.pem;
    ssl_certificate_key /etc/nginx/zhengshu/privkey.pem;
    ssl_protocols TLSv1 TLSv1.1 TLSv1.2;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:HIGH:!aNULL:!MD5:!RC4:!DHE;
    ssl_prefer_server_ciphers on;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;


    location /  {
        proxy_set_header  Host $host;
        proxy_set_header  x-real-ip $remote_addr;
        proxy_set_header  x-forwarded-for $remote_addr;
        proxy_pass        http://goapi;
    }

}