upstream gochat_ws {
    server 127.0.0.1:7000;
}

server {
    listen       443 ssl;
    listen       [::]:443 ssl;
    server_name testgosocket.fqparty.com;
    access_log  /var/log/gochat.access.log;
    error_log  /var/log/gochat.error.log;

        ssl on;
        ssl_certificate /etc/nginx/zhengshu/fullchain.pem;
    ssl_certificate_key /etc/nginx/zhengshu/privkey.pem;
    ssl_protocols TLSv1 TLSv1.1 TLSv1.2;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:HIGH:!aNULL:!MD5:!RC4:!DHE;
    ssl_prefer_server_ciphers on;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;


    location /ws  {
        proxy_set_header  Host $host;
        proxy_set_header  x-real-ip $remote_addr;
        proxy_set_header  x-forwarded-for $remote_addr;
        proxy_http_version 1.1;
            proxy_connect_timeout 4s;                #配置点1
            proxy_read_timeout 60s;                  #配置点2，如果没效，可以考虑这个时间配置长一点
            proxy_send_timeout 12s;                  #配置点3
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "Upgrade";
        proxy_pass        http://gochat_ws;
    }

}