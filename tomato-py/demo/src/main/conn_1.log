2020-12-23 10:37:30,570 I 4437004312 configure.initByFile rootPath= /Users/<USER>/yuyin/code/py/fanqie/tomato-py/demo/config
2020-12-23 10:37:30,572 I 4437004312 serverMap= {u'conn_1': {u'serverId': u'conn_1', u'agentId': u'agent_1', u'machine': {u'internet': u'127.0.0.1', u'intranet': u'127.0.0.1', u'ip': u'127.0.0.1', u'password': u'xingyue@123', u'user': u'xingyue', u'ssh': 22}, u'frontend': {u'maxConnection': 5000, u'listenings': {u'tcp': {u'port': 9100}, u'ws': {u'port': 9200}}}, 'serverType': u'conn'}, u'user_1': {u'serverId': u'user_1', u'agentId': u'agent_1', u'machine': {u'internet': u'127.0.0.1', u'intranet': u'127.0.0.1', u'ip': u'127.0.0.1', u'password': u'xingyue@123', u'user': u'xingyue', u'ssh': 22}, 'serverType': u'user'}, u'http_1': {u'serverId': u'http_1', u'agentId': u'agent_1', u'machine': {u'internet': u'127.0.0.1', u'intranet': u'127.0.0.1', u'ip': u'127.0.0.1', u'password': u'xingyue@123', u'user': u'xingyue', u'ssh': 22}, u'http': {u'port': 9000}, 'serverType': u'http'}}
2020-12-23 10:37:30,572 I 4437004312 AddAgent {u'agentId': u'agent_1', u'host': u'127.0.0.1', u'port': 6379, u'db': 0}
2020-12-23 10:37:30,572 D 4437004312 decorator.listPackageModules pkgName= tomato.common.remote.frontend recursion= True
2020-12-23 10:37:30,574 I 4437004312 decorator.registerRemoteMethod serverType= frontend serviceName= tomato.remote.frontend.session_remote methodName= kickSession module= <module 'tomato.common.remote.frontend.session_remote' from '/Users/<USER>/yuyin/code/py/fanqie/tomato-py/src/tomato/common/remote/frontend/session_remote.pyc'> method= 4436853368 decorParams= {'type': 'remoteMethod', 'methodName': 'kickSession', 'org': <function kickSession at 0x00000001087502f0>, 'serverType': 'frontend', 'serviceName': 'tomato.remote.frontend.session_remote'} org= <function kickSession at 0x00000001087502f0>
2020-12-23 10:37:30,575 I 4437004312 decorator.registerRemoteMethod serverType= frontend serviceName= tomato.remote.frontend.session_remote methodName= pushMessage module= <module 'tomato.common.remote.frontend.session_remote' from '/Users/<USER>/yuyin/code/py/fanqie/tomato-py/src/tomato/common/remote/frontend/session_remote.pyc'> method= 4436853608 decorParams= {'type': 'remoteMethod', 'methodName': 'pushMessage', 'org': <function pushMessage at 0x00000001087503e0>, 'serverType': 'frontend', 'serviceName': 'tomato.remote.frontend.session_remote'} org= <function pushMessage at 0x00000001087503e0>
2020-12-23 10:37:30,575 I 4437004312 decorator.registerRemoteMethod serverType= frontend serviceName= tomato.remote.frontend.session_remote methodName= pushRawData module= <module 'tomato.common.remote.frontend.session_remote' from '/Users/<USER>/yuyin/code/py/fanqie/tomato-py/src/tomato/common/remote/frontend/session_remote.pyc'> method= 4436853848 decorParams= {'type': 'remoteMethod', 'methodName': 'pushRawData', 'org': <function pushRawData at 0x00000001087504d0>, 'serverType': 'frontend', 'serviceName': 'tomato.remote.frontend.session_remote'} org= <function pushRawData at 0x00000001087504d0>
2020-12-23 10:37:30,575 D 4437004312 decorator.listPackageModules pkgName= tomato.common.remote.backend recursion= True
2020-12-23 10:37:30,576 D 4437004312 decorator.listPackageModules pkgName= tomato.common.remote.all recursion= True
2020-12-23 10:37:30,577 I 4437004312 decorator.registerRemoteMethod serverType= all serviceName= tomato.common.remote.all.hotfix_remote methodName= hotfix module= <module 'tomato.common.remote.all.hotfix_remote' from '/Users/<USER>/yuyin/code/py/fanqie/tomato-py/src/tomato/common/remote/all/hotfix_remote.pyc'> method= 4436854088 decorParams= {'type': 'remoteMethod', 'methodName': 'hotfix', 'org': <function hotfix at 0x00000001087505c0>, 'serverType': 'all', 'serviceName': 'tomato.common.remote.all.hotfix_remote'} org= <function hotfix at 0x00000001087505c0>
2020-12-23 10:37:30,577 I 4437004312 connectRedis host= 127.0.0.1 port= 6379 db= 0 password= None
2020-12-23 10:37:30,580 I 4437004312 Open sender conn_1 {u'agentId': u'agent_1', u'host': u'127.0.0.1', u'port': 6379, u'db': 0}
2020-12-23 10:37:30,581 I 4437004312 connectSubscriber host= 127.0.0.1 port= 6379 db= 0 password= None
2020-12-23 10:37:30,583 I 4437004312 Open receiver conn_1 {u'agentId': u'agent_1', u'host': u'127.0.0.1', u'port': 6379, u'db': 0}
2020-12-23 10:37:30,583 I 4437004312 Receiving serverId= conn_1
2020-12-23 10:37:30,585 I 4437004312 TTConnectionServer.start proto= tcp conf= {u'port': 9100}
2020-12-23 10:37:30,586 I 4437004312 TTConnectionServer.start proto= ws conf= {u'port': 9200}
2020-12-23 10:37:30,586 I 4437004312 TTApp.start ok
2020-12-23 10:37:35,270 I 4437198120 Main loop terminated.
2020-12-23 10:37:52,614 I 4605334816 configure.initByFile rootPath= /Users/<USER>/yuyin/code/py/fanqie/tomato-py/demo/config
2020-12-23 10:37:52,616 I 4605334816 serverMap= {u'conn_1': {u'serverId': u'conn_1', u'agentId': u'agent_1', u'machine': {u'internet': u'127.0.0.1', u'intranet': u'127.0.0.1', u'ip': u'127.0.0.1', u'password': u'xingyue@123', u'user': u'xingyue', u'ssh': 22}, u'frontend': {u'maxConnection': 5000, u'listenings': {u'tcp': {u'port': 9100}, u'ws': {u'port': 9200}}}, 'serverType': u'conn'}, u'user_1': {u'serverId': u'user_1', u'agentId': u'agent_1', u'machine': {u'internet': u'127.0.0.1', u'intranet': u'127.0.0.1', u'ip': u'127.0.0.1', u'password': u'xingyue@123', u'user': u'xingyue', u'ssh': 22}, 'serverType': u'user'}, u'http_1': {u'serverId': u'http_1', u'agentId': u'agent_1', u'machine': {u'internet': u'127.0.0.1', u'intranet': u'127.0.0.1', u'ip': u'127.0.0.1', u'password': u'xingyue@123', u'user': u'xingyue', u'ssh': 22}, u'http': {u'port': 9000}, 'serverType': u'http'}}
2020-12-23 10:37:52,616 I 4605334816 AddAgent {u'agentId': u'agent_1', u'host': u'127.0.0.1', u'port': 6379, u'db': 0}
2020-12-23 10:37:52,616 D 4605334816 decorator.listPackageModules pkgName= tomato.common.remote.frontend recursion= True
2020-12-23 10:37:52,618 I 4605334816 decorator.registerRemoteMethod serverType= frontend serviceName= tomato.remote.frontend.session_remote methodName= kickSession module= <module 'tomato.common.remote.frontend.session_remote' from '/Users/<USER>/yuyin/code/py/fanqie/tomato-py/src/tomato/common/remote/frontend/session_remote.pyc'> method= 4605174872 decorParams= {'type': 'remoteMethod', 'methodName': 'kickSession', 'org': <function kickSession at 0x00000001127d64d0>, 'serverType': 'frontend', 'serviceName': 'tomato.remote.frontend.session_remote'} org= <function kickSession at 0x00000001127d64d0>
2020-12-23 10:37:52,619 I 4605334816 decorator.registerRemoteMethod serverType= frontend serviceName= tomato.remote.frontend.session_remote methodName= pushMessage module= <module 'tomato.common.remote.frontend.session_remote' from '/Users/<USER>/yuyin/code/py/fanqie/tomato-py/src/tomato/common/remote/frontend/session_remote.pyc'> method= 4605175112 decorParams= {'type': 'remoteMethod', 'methodName': 'pushMessage', 'org': <function pushMessage at 0x00000001127d65c0>, 'serverType': 'frontend', 'serviceName': 'tomato.remote.frontend.session_remote'} org= <function pushMessage at 0x00000001127d65c0>
2020-12-23 10:37:52,619 I 4605334816 decorator.registerRemoteMethod serverType= frontend serviceName= tomato.remote.frontend.session_remote methodName= pushRawData module= <module 'tomato.common.remote.frontend.session_remote' from '/Users/<USER>/yuyin/code/py/fanqie/tomato-py/src/tomato/common/remote/frontend/session_remote.pyc'> method= 4605175352 decorParams= {'type': 'remoteMethod', 'methodName': 'pushRawData', 'org': <function pushRawData at 0x00000001127d66b0>, 'serverType': 'frontend', 'serviceName': 'tomato.remote.frontend.session_remote'} org= <function pushRawData at 0x00000001127d66b0>
2020-12-23 10:37:52,619 D 4605334816 decorator.listPackageModules pkgName= tomato.common.remote.backend recursion= True
2020-12-23 10:37:52,620 D 4605334816 decorator.listPackageModules pkgName= tomato.common.remote.all recursion= True
2020-12-23 10:37:52,621 I 4605334816 decorator.registerRemoteMethod serverType= all serviceName= tomato.common.remote.all.hotfix_remote methodName= hotfix module= <module 'tomato.common.remote.all.hotfix_remote' from '/Users/<USER>/yuyin/code/py/fanqie/tomato-py/src/tomato/common/remote/all/hotfix_remote.pyc'> method= 4605175592 decorParams= {'type': 'remoteMethod', 'methodName': 'hotfix', 'org': <function hotfix at 0x00000001127d67a0>, 'serverType': 'all', 'serviceName': 'tomato.common.remote.all.hotfix_remote'} org= <function hotfix at 0x00000001127d67a0>
2020-12-23 10:37:52,621 I 4605334816 connectRedis host= 127.0.0.1 port= 6379 db= 0 password= None
2020-12-23 10:37:52,624 I 4605334816 Open sender conn_1 {u'agentId': u'agent_1', u'host': u'127.0.0.1', u'port': 6379, u'db': 0}
2020-12-23 10:37:52,626 I 4605334816 connectSubscriber host= 127.0.0.1 port= 6379 db= 0 password= None
2020-12-23 10:37:52,629 I 4605334816 Open receiver conn_1 {u'agentId': u'agent_1', u'host': u'127.0.0.1', u'port': 6379, u'db': 0}
2020-12-23 10:37:52,630 I 4605334816 Receiving serverId= conn_1
2020-12-23 10:37:52,636 I 4605334816 TTConnectionServer.start proto= tcp conf= {u'port': 9100}
2020-12-23 10:37:52,637 I 4605334816 TTConnectionServer.start proto= ws conf= {u'port': 9200}
2020-12-23 10:37:52,637 I 4605334816 TTApp.start ok
2020-12-23 10:37:58,641 D 4605517112 TTTimer._onTimeout handler= <bound method TTConnectionServer._processTimeout of <tomato.conn.server.TTConnectionServer object at 0x0000000112829d70>> cancelled= False args= () kw= {}
2020-12-23 10:37:58,641 I 4605517112 TTConnectionServer._processTimeout curTime= 1608691078.64 timeoutCount= 0 sessionCount= 0
2020-12-23 10:38:04,647 D 4605517224 TTTimer._onTimeout handler= <bound method TTConnectionServer._processTimeout of <tomato.conn.server.TTConnectionServer object at 0x0000000112829d70>> cancelled= False args= () kw= {}
2020-12-23 10:38:04,647 I 4605517224 TTConnectionServer._processTimeout curTime= 1608691084.65 timeoutCount= 0 sessionCount= 0
2020-12-23 10:38:10,651 D 4605517280 TTTimer._onTimeout handler= <bound method TTConnectionServer._processTimeout of <tomato.conn.server.TTConnectionServer object at 0x0000000112829d70>> cancelled= False args= () kw= {}
2020-12-23 10:38:10,651 I 4605517280 TTConnectionServer._processTimeout curTime= 1608691090.65 timeoutCount= 0 sessionCount= 0
2020-12-23 10:38:12,325 I 4605512296 Main loop terminated.
2020-12-23 10:39:36,772 I 4434970344 configure.initByFile rootPath= /Users/<USER>/yuyin/code/py/fanqie/tomato-py/demo/config
2020-12-23 10:39:36,773 I 4434970344 serverMap= {u'conn_1': {u'serverId': u'conn_1', u'agentId': u'agent_1', u'machine': {u'internet': u'127.0.0.1', u'intranet': u'127.0.0.1', u'ip': u'127.0.0.1', u'password': u'xingyue@123', u'user': u'xingyue', u'ssh': 22}, u'frontend': {u'maxConnection': 5000, u'listenings': {u'tcp': {u'port': 9100}, u'ws': {u'port': 9200}}}, 'serverType': u'conn'}, u'user_1': {u'serverId': u'user_1', u'agentId': u'agent_1', u'machine': {u'internet': u'127.0.0.1', u'intranet': u'127.0.0.1', u'ip': u'127.0.0.1', u'password': u'xingyue@123', u'user': u'xingyue', u'ssh': 22}, 'serverType': u'user'}, u'http_1': {u'serverId': u'http_1', u'agentId': u'agent_1', u'machine': {u'internet': u'127.0.0.1', u'intranet': u'127.0.0.1', u'ip': u'127.0.0.1', u'password': u'xingyue@123', u'user': u'xingyue', u'ssh': 22}, u'http': {u'port': 9000}, 'serverType': u'http'}}
2020-12-23 10:39:36,774 I 4434970344 AddAgent {u'agentId': u'agent_1', u'host': u'127.0.0.1', u'port': 6379, u'db': 0}
2020-12-23 10:39:36,774 D 4434970344 decorator.listPackageModules pkgName= tomato.common.remote.frontend recursion= True
2020-12-23 10:39:36,776 I 4434970344 decorator.registerRemoteMethod serverType= frontend serviceName= tomato.remote.frontend.session_remote methodName= kickSession module= <module 'tomato.common.remote.frontend.session_remote' from '/Users/<USER>/yuyin/code/py/fanqie/tomato-py/src/tomato/common/remote/frontend/session_remote.pyc'> method= 4434761256 decorParams= {'type': 'remoteMethod', 'methodName': 'kickSession', 'org': <function kickSession at 0x00000001085516a0>, 'serverType': 'frontend', 'serviceName': 'tomato.remote.frontend.session_remote'} org= <function kickSession at 0x00000001085516a0>
2020-12-23 10:39:36,776 I 4434970344 decorator.registerRemoteMethod serverType= frontend serviceName= tomato.remote.frontend.session_remote methodName= pushMessage module= <module 'tomato.common.remote.frontend.session_remote' from '/Users/<USER>/yuyin/code/py/fanqie/tomato-py/src/tomato/common/remote/frontend/session_remote.pyc'> method= 4434761496 decorParams= {'type': 'remoteMethod', 'methodName': 'pushMessage', 'org': <function pushMessage at 0x0000000108551790>, 'serverType': 'frontend', 'serviceName': 'tomato.remote.frontend.session_remote'} org= <function pushMessage at 0x0000000108551790>
2020-12-23 10:39:36,776 I 4434970344 decorator.registerRemoteMethod serverType= frontend serviceName= tomato.remote.frontend.session_remote methodName= pushRawData module= <module 'tomato.common.remote.frontend.session_remote' from '/Users/<USER>/yuyin/code/py/fanqie/tomato-py/src/tomato/common/remote/frontend/session_remote.pyc'> method= 4434761736 decorParams= {'type': 'remoteMethod', 'methodName': 'pushRawData', 'org': <function pushRawData at 0x0000000108551880>, 'serverType': 'frontend', 'serviceName': 'tomato.remote.frontend.session_remote'} org= <function pushRawData at 0x0000000108551880>
2020-12-23 10:39:36,776 D 4434970344 decorator.listPackageModules pkgName= tomato.common.remote.backend recursion= True
2020-12-23 10:39:36,777 D 4434970344 decorator.listPackageModules pkgName= tomato.common.remote.all recursion= True
2020-12-23 10:39:36,778 I 4434970344 decorator.registerRemoteMethod serverType= all serviceName= tomato.common.remote.all.hotfix_remote methodName= hotfix module= <module 'tomato.common.remote.all.hotfix_remote' from '/Users/<USER>/yuyin/code/py/fanqie/tomato-py/src/tomato/common/remote/all/hotfix_remote.pyc'> method= 4434761976 decorParams= {'type': 'remoteMethod', 'methodName': 'hotfix', 'org': <function hotfix at 0x0000000108551970>, 'serverType': 'all', 'serviceName': 'tomato.common.remote.all.hotfix_remote'} org= <function hotfix at 0x0000000108551970>
2020-12-23 10:39:36,778 I 4434970344 connectRedis host= 127.0.0.1 port= 6379 db= 0 password= None
2020-12-23 10:39:36,783 I 4434970344 Open sender conn_1 {u'agentId': u'agent_1', u'host': u'127.0.0.1', u'port': 6379, u'db': 0}
2020-12-23 10:39:36,783 I 4434970344 connectSubscriber host= 127.0.0.1 port= 6379 db= 0 password= None
2020-12-23 10:39:36,785 I 4434970344 Open receiver conn_1 {u'agentId': u'agent_1', u'host': u'127.0.0.1', u'port': 6379, u'db': 0}
2020-12-23 10:39:36,785 I 4434970344 Receiving serverId= conn_1
2020-12-23 10:39:36,787 I 4434970344 TTConnectionServer.start proto= tcp conf= {u'port': 9100}
2020-12-23 10:39:36,787 I 4434970344 TTConnectionServer.start proto= ws conf= {u'port': 9200}
2020-12-23 10:39:36,787 I 4434970344 TTApp.start ok
2020-12-23 10:39:42,789 D 4434538800 TTTimer._onTimeout handler= <bound method TTConnectionServer._processTimeout of <tomato.conn.server.TTConnectionServer object at 0x000000010851b168>> cancelled= False args= () kw= {}
2020-12-23 10:39:42,790 I 4434538800 TTConnectionServer._processTimeout curTime= 1608691182.79 timeoutCount= 0 sessionCount= 0
2020-12-23 10:39:48,793 D 4434538912 TTTimer._onTimeout handler= <bound method TTConnectionServer._processTimeout of <tomato.conn.server.TTConnectionServer object at 0x000000010851b168>> cancelled= False args= () kw= {}
2020-12-23 10:39:48,794 I 4434538912 TTConnectionServer._processTimeout curTime= 1608691188.79 timeoutCount= 0 sessionCount= 0
2020-12-23 10:39:50,514 I 4435074192 Main loop terminated.
