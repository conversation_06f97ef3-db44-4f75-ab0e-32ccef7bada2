#!/usr/bin/env python
# -*- coding=utf-8 -*-
'''
修复模块名冲突的脚本
'''

import os
import sys
import shutil

def fix_http_module_conflict():
    """修复http模块名冲突"""
    current_dir = os.path.dirname(os.path.abspath(__file__))
    bin_dir = os.path.join(current_dir, 'deploy', 'bin')
    
    # 检查是否存在冲突的http模块
    tomato_http_dir = os.path.join(bin_dir, 'tomato', 'http')
    
    if os.path.exists(tomato_http_dir):
        print(f"发现http模块冲突: {tomato_http_dir}")
        
        # 重命名为tomato_http以避免冲突
        new_name = os.path.join(bin_dir, 'tomato', 'tomato_http')
        
        if not os.path.exists(new_name):
            print(f"重命名 {tomato_http_dir} -> {new_name}")
            shutil.move(tomato_http_dir, new_name)
            print("http模块冲突已修复")
        else:
            print("tomato_http目录已存在，跳过重命名")
    else:
        print("未发现http模块冲突")

def create_startup_script_with_path_fix():
    """创建修复路径问题的启动脚本"""
    current_dir = os.path.dirname(os.path.abspath(__file__))
    
    script_content = f'''#!/usr/bin/env python
# -*- coding=utf-8 -*-
import os
import sys

# 确保标准库路径优先
import importlib.util
import types

# 移除可能冲突的路径
paths_to_remove = []
for path in sys.path:
    if 'tomato' in path.lower() and 'http' in os.listdir(path) if os.path.isdir(path) else False:
        paths_to_remove.append(path)

for path in paths_to_remove:
    if path in sys.path:
        sys.path.remove(path)

# 确保标准库在最前面
import http.cookiejar  # 测试标准库导入

# 现在添加项目路径
current_dir = r"{current_dir}"
bin_dir = os.path.join(current_dir, "deploy", "bin")
config_path = os.path.join(current_dir, "deploy", "config")
logs_path = os.path.join(current_dir, "deploy", "logs")

# 添加到路径末尾
sys.path.append(bin_dir)

# 设置环境变量
os.environ["PYTHONPATH"] = bin_dir

# 导入并运行
try:
    from tomato.server import server
    import fqparty
    
    service_id = sys.argv[1] if len(sys.argv) > 1 else "CO000001"
    print(f"启动服务: {{service_id}}")
    
    server.runWithFileConf(service_id, config_path, fqparty.app, [])
except Exception as e:
    print(f"启动失败: {{e}}")
    import traceback
    traceback.print_exc()
    input("按回车键退出...")
'''
    
    script_path = os.path.join(current_dir, "start_service_fixed.py")
    with open(script_path, 'w', encoding='utf-8') as f:
        f.write(script_content)
    
    print(f"已创建修复版启动脚本: {script_path}")

def main():
    print("=" * 60)
    print("修复模块冲突")
    print("=" * 60)
    
    # 1. 修复http模块冲突
    fix_http_module_conflict()
    
    # 2. 创建修复版启动脚本
    create_startup_script_with_path_fix()
    
    print("\n修复完成！")
    print("现在可以尝试运行: python start_service_fixed.py CO000001")

if __name__ == '__main__':
    main()
