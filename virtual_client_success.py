#!/usr/bin/env python
# -*- coding=utf-8 -*-
'''
成功的虚拟客户端 - 使用原生socket实现WebSocket协议
'''

import socket
import base64
import hashlib
import struct
import json
import time
import threading

class WebSocketClient:
    """原生WebSocket客户端实现"""
    
    def __init__(self, host, port):
        self.host = host
        self.port = port
        self.socket = None
        self.connected = False
        
    def connect(self):
        """连接到WebSocket服务器"""
        try:
            # 创建socket连接
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.connect((self.host, self.port))
            
            # 发送WebSocket握手请求
            key = base64.b64encode(b'test_key_123456789').decode()
            
            handshake = (
                f"GET / HTTP/1.1\r\n"
                f"Host: {self.host}:{self.port}\r\n"
                f"Upgrade: websocket\r\n"
                f"Connection: Upgrade\r\n"
                f"Sec-WebSocket-Key: {key}\r\n"
                f"Sec-WebSocket-Version: 13\r\n"
                f"User-Agent: VirtualClient/1.0\r\n"
                f"\r\n"
            )
            
            print(f"发送握手请求:")
            print(handshake)
            
            self.socket.send(handshake.encode())
            
            # 接收握手响应
            response = self.socket.recv(1024).decode()
            print(f"收到握手响应:")
            print(response)
            
            if "101" in response and "Upgrade" in response:
                print("✅ WebSocket握手成功!")
                self.connected = True
                return True
            else:
                print("❌ WebSocket握手失败!")
                return False
                
        except Exception as e:
            print(f"❌ 连接失败: {e}")
            return False
    
    def send_text_frame(self, data):
        """发送文本帧"""
        if not self.connected:
            print("❌ 未连接到服务器")
            return False
            
        try:
            # 构造WebSocket文本帧
            payload = data.encode('utf-8')
            payload_len = len(payload)
            
            # 帧头
            frame = bytearray()
            frame.append(0x81)  # FIN=1, opcode=1 (text frame)
            
            # 负载长度
            if payload_len < 126:
                frame.append(0x80 | payload_len)  # MASK=1, payload length
            elif payload_len < 65536:
                frame.append(0x80 | 126)  # MASK=1, extended payload length
                frame.extend(struct.pack('!H', payload_len))
            else:
                frame.append(0x80 | 127)  # MASK=1, extended payload length
                frame.extend(struct.pack('!Q', payload_len))
            
            # 掩码键
            mask_key = b'\x12\x34\x56\x78'
            frame.extend(mask_key)
            
            # 掩码负载
            masked_payload = bytearray()
            for i, byte in enumerate(payload):
                masked_payload.append(byte ^ mask_key[i % 4])
            
            frame.extend(masked_payload)
            
            print(f"发送WebSocket帧: {len(frame)} 字节")
            print(f"帧数据: {frame.hex()}")
            
            self.socket.send(frame)
            return True
            
        except Exception as e:
            print(f"❌ 发送失败: {e}")
            return False
    
    def receive_frame(self, timeout=10):
        """接收WebSocket帧"""
        if not self.connected:
            print("❌ 未连接到服务器")
            return None
            
        try:
            # 设置超时
            self.socket.settimeout(timeout)
            
            # 读取帧头
            header = self.socket.recv(2)
            if len(header) < 2:
                return None
                
            fin = (header[0] & 0x80) != 0
            opcode = header[0] & 0x0F
            masked = (header[1] & 0x80) != 0
            payload_len = header[1] & 0x7F
            
            # 读取扩展长度
            if payload_len == 126:
                extended_len = self.socket.recv(2)
                payload_len = struct.unpack('!H', extended_len)[0]
            elif payload_len == 127:
                extended_len = self.socket.recv(8)
                payload_len = struct.unpack('!Q', extended_len)[0]
            
            # 读取掩码键（如果有）
            mask_key = None
            if masked:
                mask_key = self.socket.recv(4)
            
            # 读取负载
            payload = self.socket.recv(payload_len)
            
            # 解掩码（如果需要）
            if masked and mask_key:
                unmasked_payload = bytearray()
                for i, byte in enumerate(payload):
                    unmasked_payload.append(byte ^ mask_key[i % 4])
                payload = bytes(unmasked_payload)
            
            print(f"收到WebSocket帧: opcode={opcode}, len={payload_len}")
            
            if opcode == 1:  # 文本帧
                return payload.decode('utf-8')
            elif opcode == 2:  # 二进制帧
                return payload
            else:
                print(f"未知帧类型: {opcode}")
                return None
                
        except socket.timeout:
            print("⚠️  接收超时")
            return None
        except Exception as e:
            print(f"❌ 接收失败: {e}")
            return None
    
    def close(self):
        """关闭连接"""
        if self.socket:
            self.socket.close()
            self.connected = False

def test_virtual_client():
    """测试虚拟客户端"""
    print("=" * 60)
    print("虚拟WebSocket客户端测试")
    print("=" * 60)
    
    # 创建客户端
    client = WebSocketClient('127.0.0.1', 9200)
    
    try:
        # 连接服务器
        if not client.connect():
            print("❌ 连接失败")
            return False
        
        # 等待连接稳定
        time.sleep(1)
        
        # 测试1: 发送用户绑定消息
        print(f"\n--- 测试1: 用户绑定 ---")
        bind_message = {
            "route": "/user/bind",
            "userId": "test_user_123",
            "token": "simple_test_token_456"
        }
        
        message_str = json.dumps(bind_message)
        print(f"发送: {message_str}")
        
        if client.send_text_frame(message_str):
            print("✅ 消息发送成功")
            
            # 等待响应
            print("等待服务器响应...")
            response = client.receive_frame(timeout=15)
            
            if response:
                print(f"🎉 收到响应: {response}")
                
                try:
                    response_data = json.loads(response)
                    print("✅ 解析后的响应:")
                    print(json.dumps(response_data, indent=2, ensure_ascii=False))
                    
                    if response_data.get("ec") == 0:
                        print("🎉 用户绑定成功!")
                        return True
                    else:
                        print(f"⚠️  绑定失败: {response_data.get('info', '未知错误')}")
                        
                except json.JSONDecodeError:
                    print(f"响应不是JSON格式，但收到了响应: {response}")
                    return True  # 至少收到了响应
                    
            else:
                print("⚠️  没有收到响应")
        
        # 测试2: 发送进入房间消息
        print(f"\n--- 测试2: 进入房间 ---")
        room_message = {
            "route": "/user/bindAndEnterRoom",
            "roomId": "test_room_001",
            "authToken": "simple_test_token_456",
            "platform": "VirtualClient",
            "version": "1.0.0"
        }
        
        message_str = json.dumps(room_message)
        print(f"发送: {message_str}")
        
        if client.send_text_frame(message_str):
            print("✅ 消息发送成功")
            
            # 等待响应
            print("等待服务器响应...")
            response = client.receive_frame(timeout=15)
            
            if response:
                print(f"🎉 收到响应: {response}")
                
                try:
                    response_data = json.loads(response)
                    print("✅ 解析后的响应:")
                    print(json.dumps(response_data, indent=2, ensure_ascii=False))
                    return True
                except json.JSONDecodeError:
                    print(f"响应不是JSON格式，但收到了响应: {response}")
                    return True  # 至少收到了响应
                    
            else:
                print("⚠️  没有收到响应")
        
        # 监听推送消息
        print(f"\n--- 监听服务器推送 ---")
        print("监听10秒，等待服务器推送消息...")
        
        for i in range(10):
            response = client.receive_frame(timeout=1)
            if response:
                print(f"\n📨 收到服务器推送: {response}")
                
                try:
                    msg_data = json.loads(response)
                    print("解析后的推送消息:")
                    print(json.dumps(msg_data, indent=2, ensure_ascii=False))
                    return True
                except json.JSONDecodeError:
                    print(f"推送消息不是JSON格式: {response}")
                    return True
            else:
                print(".", end="", flush=True)
        
        print(f"\n\n❌ 没有收到任何服务器响应")
        return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        client.close()

def check_test_token():
    """检查测试token是否存在"""
    print("=" * 60)
    print("检查测试token")
    print("=" * 60)
    
    try:
        import redis
        r = redis.Redis(host='127.0.0.1', port=6379, db=1)
        
        token = "simple_test_token_456"
        user_id = r.get(token)
        
        if user_id:
            print(f"✅ Token验证成功: {token} -> {user_id.decode()}")
            return True
        else:
            print(f"❌ Token不存在: {token}")
            return False
            
    except Exception as e:
        print(f"❌ Redis检查失败: {e}")
        return False

def main():
    """主函数"""
    # 先检查token
    if not check_test_token():
        print("\n请先运行: python setup_simple_test_token.py")
        return
    
    # 然后测试连接
    success = test_virtual_client()
    
    print("\n" + "=" * 60)
    print("虚拟客户端测试结果")
    print("=" * 60)
    
    if success:
        print("🎉 虚拟客户端测试成功!")
        print("✅ 服务器能够接收和处理WebSocket消息")
        print("✅ 服务器工作完全正常")
    else:
        print("❌ 虚拟客户端测试失败!")
        print("❌ 可能存在协议兼容性问题")
        
        print("\n建议:")
        print("1. 检查服务器日志中的dataReceived消息")
        print("2. 确认WebSocket帧格式是否正确")
        print("3. 尝试使用真实客户端测试")
    
    print("=" * 60)

if __name__ == '__main__':
    print("开始虚拟WebSocket客户端测试...")
    main()
