#!/usr/bin/env python
# -*- coding=utf-8 -*-
'''
调试版本的服务启动脚本，显示详细错误信息
'''

import os
import sys
import traceback

def main():
    if len(sys.argv) < 2:
        print("用法: python debug_start_service.py <服务ID>")
        print("可用的服务ID:")
        print("  CO000001 - 连接服务器")
        print("  US000001 - 用户服务器")
        print("  HT000001 - HTTP服务器")
        print("  RM000001 - 房间服务器")
        return
    
    service_id = sys.argv[1]
    
    # 项目路径配置
    current_dir = os.path.dirname(os.path.abspath(__file__))
    bin_dir = os.path.join(current_dir, 'deploy', 'bin')
    config_path = os.path.join(current_dir, 'deploy', 'config')
    logs_path = os.path.join(current_dir, 'deploy', 'logs')
    
    # 确保日志目录存在
    os.makedirs(logs_path, exist_ok=True)
    
    # 设置环境变量
    env = dict(os.environ)
    env["PYTHONPATH"] = f"{current_dir};{bin_dir}"
    
    print(f"调试启动服务 {service_id}")
    print("=" * 50)
    print(f"当前目录: {current_dir}")
    print(f"bin目录: {bin_dir}")
    print(f"配置目录: {config_path}")
    print(f"日志目录: {logs_path}")
    print(f"Python路径: {sys.path}")
    print("=" * 50)
    
    try:
        # 添加路径
        sys.path.insert(0, bin_dir)
        
        print("1. 导入stackless模块...")
        import stackless
        print("   ✅ stackless模块导入成功")
        
        print("2. 导入tomato模块...")
        import tomato
        from tomato.config import configure
        from tomato.core import mainloop
        from tomato.utils import ttlog
        print("   ✅ tomato模块导入成功")
        
        print("3. 导入fqparty模块...")
        import fqparty
        print("   ✅ fqparty模块导入成功")
        
        print("4. 初始化配置...")
        configure.initByFile(config_path)
        print("   ✅ 配置初始化成功")
        
        print("5. 设置日志级别...")
        logLevel = configure.loadJson('server.tomato.global', {}).get('log', {}).get('level', ttlog.INFO)
        ttlog.setLevel(logLevel)
        print(f"   ✅ 日志级别设置为: {logLevel}")
        
        print("6. 初始化tomato应用...")
        tomato.app.appArgs = []
        tomato.app.init(service_id)
        print("   ✅ tomato应用初始化成功")
        
        print("7. 初始化fqparty应用...")
        fqparty.app.init()
        print("   ✅ fqparty应用初始化成功")
        
        print("8. 启动fqparty应用...")
        fqparty.app.start()
        print("   ✅ fqparty应用启动成功")
        
        print("9. 启动tomato应用...")
        tomato.app.start()
        print("   ✅ tomato应用启动成功")
        
        print("\n" + "=" * 50)
        print(f"🎉 服务 {service_id} 启动成功！")
        print("=" * 50)
        
        # 显示服务信息
        if service_id == 'HT000001':
            print("HTTP服务: http://127.0.0.1:9000/")
        elif service_id == 'CO000001':
            print("WebSocket服务: ws://127.0.0.1:9200/")
        
        print("按 Ctrl+C 停止服务")
        print("=" * 50)
        
        # 启动主循环
        from tomato.core import reactor
        reactor.run()
        
    except KeyboardInterrupt:
        print(f"\n服务 {service_id} 已停止")
    except Exception as e:
        print(f"\n❌ 启动服务 {service_id} 失败!")
        print(f"错误类型: {type(e).__name__}")
        print(f"错误信息: {str(e)}")
        print("\n详细错误信息:")
        print("-" * 50)
        traceback.print_exc()
        print("-" * 50)
        
        # 尝试分析常见问题
        error_str = str(e).lower()
        if 'no module named' in error_str:
            print("\n💡 可能的解决方案:")
            print("1. 检查模块是否正确安装")
            print("2. 检查PYTHONPATH是否正确设置")
            print("3. 检查项目结构是否完整")
        elif 'permission denied' in error_str:
            print("\n💡 可能的解决方案:")
            print("1. 检查文件权限")
            print("2. 尝试以管理员权限运行")
        elif 'address already in use' in error_str:
            print("\n💡 可能的解决方案:")
            print("1. 检查端口是否被其他程序占用")
            print("2. 停止其他相同的服务")
        elif 'connection refused' in error_str:
            print("\n💡 可能的解决方案:")
            print("1. 检查Redis是否运行")
            print("2. 检查数据库连接配置")
        
        input("\n按回车键退出...")

if __name__ == '__main__':
    main()
