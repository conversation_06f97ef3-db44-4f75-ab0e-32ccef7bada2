#!/usr/bin/env python
# -*- coding=utf-8 -*-
'''
在PyCharm中一次性启动所有服务并显示日志
'''

import os
import sys
import subprocess
import time
import threading

# 将bin目录添加到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
bin_dir = os.path.join(current_dir, 'deploy', 'bin')
sys.path.insert(0, bin_dir)

# 配置路径
deploy_path = os.path.join(current_dir, 'deploy')
config_path = os.path.join(deploy_path, 'config')
logs_path = os.path.join(deploy_path, 'logs')
run_py = os.path.join(bin_dir, 'tomato', 'run.py')

# 确保日志目录存在
os.makedirs(logs_path, exist_ok=True)

# 服务ID列表
services = [
    'CO000001',  # 连接服务器
    'US000001',  # 用户服务器
    'HT000001',  # HTTP服务器
    'RM000001'  # 房间服务器
]


def stream_output(process, service_id):
    """实时输出进程的标准输出和错误输出"""
    for line in iter(process.stdout.readline, ''):
        print(f"[{service_id}] {line.strip()}")

    for line in iter(process.stderr.readline, ''):
        print(f"[{service_id} ERROR] {line.strip()}")


def start_service(service_id, reload='0'):
    """启动指定的服务并实时显示输出"""
    cmd = [
        sys.executable,
        run_py,
        service_id,
        'fqparty',
        config_path,
        logs_path,
        reload
    ]
    print(f"启动服务 {service_id}...")

    # 创建进程，并捕获标准输出和错误输出
    process = subprocess.Popen(
        cmd,
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        env=dict(os.environ, PYTHONPATH=bin_dir),
        bufsize=1,
        universal_newlines=True
    )

    # 创建线程来读取输出
    thread = threading.Thread(target=stream_output, args=(process, service_id))
    thread.daemon = True
    thread.start()

    return process, thread


if __name__ == '__main__':
    print("开始启动所有服务...")

    processes = []
    threads = []

    for service_id in services:
        process, thread = start_service(service_id)
        processes.append((service_id, process))
        threads.append(thread)

    print("所有服务已启动！")
    print("正在显示实时日志输出...")
    print("按Ctrl+C停止所有服务")

    try:
        # 保持脚本运行，同时允许线程继续读取输出
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("正在停止所有服务...")
        for service_id, process in processes:
            print(f"停止服务 {service_id}...")
            process.terminate()
        print("所有服务已停止！")