#!/usr/bin/env python
# -*- coding=utf-8 -*-
'''
完全兼容wsold协议的WebSocket测试客户端
能够正确与服务器通信并获得响应
'''

import socket
import base64
import hashlib
import struct
import json
import time
import os
import threading

class CompatibleWebSocketClient:
    """完全兼容wsold协议的WebSocket客户端"""
    
    def __init__(self, host, port):
        self.host = host
        self.port = port
        self.socket = None
        self.connected = False
        self.receive_thread = None
        self.running = False
        
    def connect(self):
        """连接到WebSocket服务器"""
        try:
            # 创建socket连接
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.connect((self.host, self.port))
            
            print(f"✅ TCP连接建立成功: {self.host}:{self.port}")
            
            # 发送WebSocket握手请求
            key = base64.b64encode(os.urandom(16)).decode()
            
            handshake = (
                f"GET / HTTP/1.1\r\n"
                f"Host: {self.host}:{self.port}\r\n"
                f"Upgrade: websocket\r\n"
                f"Connection: Upgrade\r\n"
                f"Sec-WebSocket-Key: {key}\r\n"
                f"Sec-WebSocket-Version: 13\r\n"
                f"User-Agent: CompatibleWebSocketClient/1.0\r\n"
                f"\r\n"
            )
            
            print(f"📤 发送握手请求:")
            print(handshake)
            
            self.socket.send(handshake.encode())
            
            # 接收握手响应
            response = self.socket.recv(1024).decode()
            print(f"📥 收到握手响应:")
            print(response)
            
            if "101" in response and "Upgrade" in response:
                print("✅ WebSocket握手成功!")
                self.connected = True
                self.running = True
                
                # 启动接收线程
                self.receive_thread = threading.Thread(target=self._receive_loop)
                self.receive_thread.daemon = True
                self.receive_thread.start()
                
                return True
            else:
                print("❌ WebSocket握手失败!")
                return False
                
        except Exception as e:
            print(f"❌ 连接失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def create_websocket_frame(self, data, opcode=0x1):
        """创建标准RFC6455 WebSocket帧"""
        if isinstance(data, str):
            data = data.encode('utf-8')
        
        print(f"🔧 创建WebSocket帧:")
        print(f"   操作码: {opcode} ({'文本' if opcode == 0x1 else '二进制' if opcode == 0x2 else '其他'})")
        print(f"   数据长度: {len(data)} 字节")
        print(f"   数据内容: {data}")
        
        # RFC6455帧格式：
        # FIN(1) + RSV(3) + OPCODE(4) + MASK(1) + PAYLOAD_LEN(7/16/64) + MASKING_KEY(32) + PAYLOAD
        
        # 第一个字节：FIN=1, RSV=000, OPCODE=opcode
        first_byte = 0x80 | opcode  # FIN=1
        
        # 计算负载长度和第二个字节
        payload_len = len(data)
        
        if payload_len < 126:
            # 短负载：7位长度
            second_byte = 0x80 | payload_len  # MASK=1 + 长度
            frame_header = struct.pack('!BB', first_byte, second_byte)
        elif payload_len < 65536:
            # 中等负载：16位长度
            second_byte = 0x80 | 126  # MASK=1 + 126
            frame_header = struct.pack('!BBH', first_byte, second_byte, payload_len)
        else:
            # 长负载：64位长度
            second_byte = 0x80 | 127  # MASK=1 + 127
            frame_header = struct.pack('!BBQ', first_byte, second_byte, payload_len)
        
        # 生成4字节掩码
        masking_key = os.urandom(4)
        
        # 对负载进行掩码处理
        masked_payload = bytearray()
        for i, byte in enumerate(data):
            masked_payload.append(byte ^ masking_key[i % 4])
        
        # 组装完整帧
        frame = frame_header + masking_key + bytes(masked_payload)
        
        print(f"   完整帧: {len(frame)} 字节")
        print(f"   帧头: {frame_header.hex()}")
        print(f"   掩码: {masking_key.hex()}")
        print(f"   掩码负载: {bytes(masked_payload).hex()}")
        print(f"   完整帧(十六进制): {frame.hex()}")
        
        return frame
    
    def send_message(self, message, opcode=0x1):
        """发送WebSocket消息"""
        if not self.connected:
            print("❌ 未连接到服务器")
            return False
            
        try:
            print(f"\n🚀 发送WebSocket消息...")
            print(f"   消息内容: {message}")
            
            frame = self.create_websocket_frame(message, opcode)
            
            print(f"\n📤 发送帧到服务器...")
            sent_bytes = self.socket.send(frame)
            print(f"✅ 帧发送成功: {sent_bytes} 字节")
            
            return True
            
        except Exception as e:
            print(f"❌ 发送失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def _receive_loop(self):
        """接收循环（在单独线程中运行）"""
        print("📥 启动接收线程...")
        
        while self.running and self.connected:
            try:
                # 设置短超时以便能够响应停止信号
                self.socket.settimeout(1.0)
                
                data = self.socket.recv(4096)
                
                if data:
                    print(f"\n📨 收到服务器数据: {len(data)} 字节")
                    print(f"   原始数据: {data}")
                    print(f"   十六进制: {data.hex()}")
                    
                    # 尝试解析WebSocket帧
                    self._parse_websocket_frame(data)
                    
                else:
                    print("📥 收到空数据，连接可能已关闭")
                    break
                    
            except socket.timeout:
                # 超时是正常的，继续循环
                continue
            except Exception as e:
                if self.running:
                    print(f"❌ 接收数据时出错: {e}")
                break
        
        print("📥 接收线程结束")
    
    def _parse_websocket_frame(self, data):
        """解析WebSocket帧"""
        try:
            if len(data) < 2:
                print("   数据太短，无法解析WebSocket帧")
                return
            
            first_byte = data[0]
            second_byte = data[1]
            
            fin = (first_byte & 0x80) >> 7
            opcode = first_byte & 0x0F
            mask = (second_byte & 0x80) >> 7
            payload_len = second_byte & 0x7F
            
            print(f"   WebSocket帧解析:")
            print(f"     FIN: {fin}")
            print(f"     OPCODE: {opcode} ({'文本' if opcode == 0x1 else '二进制' if opcode == 0x2 else '关闭' if opcode == 0x8 else '其他'})")
            print(f"     MASK: {mask}")
            print(f"     PAYLOAD_LEN: {payload_len}")
            
            # 计算负载起始位置
            header_len = 2
            if payload_len == 126:
                if len(data) < 4:
                    print("   数据不完整（扩展长度）")
                    return
                payload_len = struct.unpack('!H', data[2:4])[0]
                header_len = 4
            elif payload_len == 127:
                if len(data) < 10:
                    print("   数据不完整（扩展长度）")
                    return
                payload_len = struct.unpack('!Q', data[2:10])[0]
                header_len = 10
            
            print(f"     实际负载长度: {payload_len}")
            
            # 处理掩码
            if mask:
                if len(data) < header_len + 4:
                    print("   数据不完整（掩码）")
                    return
                masking_key = data[header_len:header_len + 4]
                payload_start = header_len + 4
                print(f"     掩码密钥: {masking_key.hex()}")
            else:
                payload_start = header_len
            
            # 提取负载
            if len(data) < payload_start + payload_len:
                print(f"   数据不完整（负载）: 需要{payload_start + payload_len}字节，实际{len(data)}字节")
                return
            
            payload = data[payload_start:payload_start + payload_len]
            
            # 解除掩码
            if mask:
                unmasked_payload = bytearray()
                for i, byte in enumerate(payload):
                    unmasked_payload.append(byte ^ masking_key[i % 4])
                payload = bytes(unmasked_payload)
            
            print(f"     负载数据: {payload}")
            
            # 尝试解析为文本
            if opcode == 0x1:  # 文本帧
                try:
                    text = payload.decode('utf-8')
                    print(f"🎉 收到文本消息: {text}")
                    
                    # 尝试解析为JSON
                    try:
                        json_data = json.loads(text)
                        print(f"📋 JSON解析成功:")
                        print(json.dumps(json_data, indent=2, ensure_ascii=False))
                    except json.JSONDecodeError:
                        print(f"   不是JSON格式")
                        
                except UnicodeDecodeError:
                    print(f"   无法解码为UTF-8文本")
            elif opcode == 0x2:  # 二进制帧
                print(f"🎉 收到二进制消息: {payload.hex()}")
            elif opcode == 0x8:  # 关闭帧
                print(f"🔌 收到关闭帧")
                self.running = False
            
        except Exception as e:
            print(f"❌ 解析WebSocket帧时出错: {e}")
            import traceback
            traceback.print_exc()
    
    def wait_for_response(self, timeout=30):
        """等待响应（阻塞方式）"""
        print(f"\n⏰ 等待服务器响应 (超时: {timeout}秒)...")
        
        start_time = time.time()
        while time.time() - start_time < timeout:
            if not self.running or not self.connected:
                print("连接已断开")
                return False
            time.sleep(0.1)
        
        print(f"⏰ 等待超时 ({timeout}秒)")
        return False
    
    def close(self):
        """关闭连接"""
        print("🔌 关闭WebSocket连接")
        self.running = False
        self.connected = False
        
        if self.socket:
            try:
                # 发送关闭帧
                close_frame = self.create_websocket_frame(b'', opcode=0x8)
                self.socket.send(close_frame)
            except:
                pass
            
            self.socket.close()
        
        if self.receive_thread and self.receive_thread.is_alive():
            self.receive_thread.join(timeout=2)

def test_compatible_websocket():
    """测试兼容的WebSocket客户端"""
    print("=" * 80)
    print("兼容wsold协议的WebSocket客户端测试")
    print("=" * 80)
    
    # 创建客户端
    client = CompatibleWebSocketClient('127.0.0.1', 9200)
    
    try:
        # 连接服务器
        print("\n🔗 步骤1: 建立连接")
        if not client.connect():
            print("❌ 连接失败")
            return False
        
        # 等待连接稳定
        print("\n⏰ 步骤2: 等待连接稳定...")
        time.sleep(2)
        
        # 发送用户绑定消息
        print("\n📨 步骤3: 发送用户绑定消息")
        bind_message = {
            "route": "/user/bind",
            "userId": "test_user_123",
            "token": "simple_test_token_456"
        }
        
        message_str = json.dumps(bind_message)
        print(f"消息内容: {message_str}")
        
        if client.send_message(message_str):
            print("✅ 用户绑定消息发送成功")
            
            # 等待响应
            print("\n📥 步骤4: 等待服务器响应")
            client.wait_for_response(timeout=15)
            
        # 发送进入房间消息
        print("\n📨 步骤5: 发送进入房间消息")
        room_message = {
            "route": "/user/bindAndEnterRoom",
            "roomId": "test_room_001",
            "authToken": "simple_test_token_456",
            "platform": "CompatibleClient",
            "version": "1.0.0"
        }
        
        message_str = json.dumps(room_message)
        print(f"消息内容: {message_str}")
        
        if client.send_message(message_str):
            print("✅ 进入房间消息发送成功")
            
            # 等待响应
            print("\n📥 步骤6: 等待服务器响应")
            client.wait_for_response(timeout=15)
        
        # 保持连接一段时间以接收可能的推送消息
        print("\n📥 步骤7: 监听服务器推送消息")
        print("保持连接30秒，监听服务器推送...")
        
        for i in range(30):
            if not client.running or not client.connected:
                break
            time.sleep(1)
            if i % 5 == 0:
                print(f"   监听中... ({i}/30秒)")
        
        print("\n✅ 测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        client.close()

def check_test_token():
    """检查测试token是否存在"""
    print("=" * 80)
    print("检查测试token")
    print("=" * 80)
    
    try:
        import redis
        r = redis.Redis(host='127.0.0.1', port=6379, db=1)
        
        token = "simple_test_token_456"
        user_id = r.get(token)
        
        if user_id:
            print(f"✅ Token验证成功: {token} -> {user_id.decode()}")
            return True
        else:
            print(f"❌ Token不存在: {token}")
            return False
            
    except Exception as e:
        print(f"❌ Redis检查失败: {e}")
        return False

def main():
    """主函数"""
    # 先检查token
    if not check_test_token():
        print("\n请先运行: python setup_simple_test_token.py")
        return
    
    # 然后测试连接
    success = test_compatible_websocket()
    
    print("\n" + "=" * 80)
    print("兼容WebSocket测试结果")
    print("=" * 80)
    
    if success:
        print("🎉 测试成功!")
        print("✅ WebSocket连接和消息传输正常")
        print("✅ 服务器能够接收和处理消息")
        print("✅ 能够正确解析服务器响应")
    else:
        print("❌ 测试失败!")
        print("❌ 需要进一步分析问题")
    
    print("=" * 80)

if __name__ == '__main__':
    print("开始兼容WebSocket测试...")
    main()
