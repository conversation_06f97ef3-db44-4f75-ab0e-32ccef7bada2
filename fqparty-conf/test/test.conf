{"projectsPath": "/home/<USER>/live_room_service/source", "configPath": "/home/<USER>/live_room_service/source/fqparty-conf/test", "compilePath": "/home/<USER>/live_room_service/deploy/compile", "backPath": "/home/<USER>/live_room_service/deploy/back", "deployPath": "/home/<USER>/live_room_service/deploy", "http_url": "http://127.0.0.1:9000/", "HT000001": "http://127.0.0.1:9000/", "pypy": "pypy", "configRedis": {"host": "127.0.0.1", "port": 6379, "db": 0}, "appModule": "fqparty", "projects": [{"name": "tomato", "path": "tomato-py", "sources": ["src"]}, {"name": "fqparty", "path": "fqparty-py", "sources": ["src"]}]}