
import sys
import os
import traceback

# ������־
import logging
logging.basicConfig(level=logging.DEBUG, 
                   format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('RM000001')

try:
    # ���ģ���stacklessģ�鵽·��
    logger.debug("���stacklessģ��·��: %s", r"I:\live_room_service-master\live_room_service-master\deploy\bin\stackless_complete")
    sys.path.insert(0, r"I:\live_room_service-master\live_room_service-master\deploy\bin\stackless_complete")
    
    logger.debug("���bin·��: %s", r"I:\live_room_service-master\live_room_service-master\deploy\bin")
    sys.path.insert(1, r"I:\live_room_service-master\live_room_service-master\deploy\bin")
    
    # �����Ҫ��ģ��
    logger.debug("��ʼ����serverģ��")
    from tomato.server import server
    logger.debug("�ɹ�����serverģ��")
    
    # ���Redis����
    try:
        import socket
        s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        s.settimeout(1)
        s.connect(('127.0.0.1', 6379))
        s.close()
        logger.debug("Redis���ӳɹ�")
    except Exception as e:
        logger.error("Redis����ʧ��: %s", str(e))
        input("Redis����ʧ�ܣ����س����˳�...")
        sys.exit(1)
    
    # ���з���
    logger.debug("��ʼ��������: RM000001")
    server.runWithFileConf("RM000001", r"I:\live_room_service-master\live_room_service-master\deploy\config", __import__("fqparty").app, [])
except Exception as e:
    logger.error("���������г���: %s", str(e))
    logger.error(traceback.format_exc())
    input("�������󣬰��س����˳�...")
