#!/usr/bin/env python
# -*- coding=utf-8 -*-
'''
设置测试token到Redis
'''

import redis
import json

def setup_test_token():
    """设置测试token到Redis"""
    print("=" * 60)
    print("设置测试token到Redis")
    print("=" * 60)
    
    # 连接Redis
    try:
        # 连接session Redis (通常是db=1)
        session_redis = redis.Redis(host='127.0.0.1', port=6379, db=1)
        session_redis.ping()
        print("✅ 连接到session Redis成功")
        
        # 连接common Redis (通常是db=0)
        common_redis = redis.Redis(host='127.0.0.1', port=6379, db=0)
        common_redis.ping()
        print("✅ 连接到common Redis成功")
        
    except Exception as e:
        print(f"❌ 连接Redis失败: {e}")
        print("请确保Redis服务正在运行")
        return False
    
    # 设置测试数据 - 使用真实参数
    test_user_id = "100216"  # 使用房间号作为用户ID
    test_token = "072d576456d38bfc41208c404fc3c087"  # 真实token
    
    try:
        # 在session Redis中设置 token -> userId 的映射
        session_redis.set(test_token, test_user_id)
        print(f"✅ 设置token映射: {test_token} -> {test_user_id}")
        
        # 在session Redis中设置 userId -> token 的映射
        session_redis.set(test_user_id, test_token)
        print(f"✅ 设置用户映射: {test_user_id} -> {test_token}")
        
        # 验证设置
        stored_user_id = session_redis.get(test_token)
        stored_token = session_redis.get(test_user_id)
        
        if stored_user_id and stored_token:
            print(f"✅ 验证成功:")
            print(f"   token '{test_token}' -> userId '{stored_user_id.decode()}'")
            print(f"   userId '{test_user_id}' -> token '{stored_token.decode()}'")
        else:
            print("❌ 验证失败")
            return False
        
        # 设置token过期时间 (1小时)
        session_redis.expire(test_token, 3600)
        session_redis.expire(test_user_id, 3600)
        print("✅ 设置token过期时间: 1小时")
        
        print("\n" + "=" * 60)
        print("测试token设置完成!")
        print("=" * 60)
        print(f"用户ID: {test_user_id}")
        print(f"Token: {test_token}")
        print(f"房间ID: 100216")
        print("\n现在可以使用以下命令测试WebSocket连接:")
        print("python test_room_100216.py")
        print("=" * 60)
        
        return True
        
    except Exception as e:
        print(f"❌ 设置token失败: {e}")
        return False

def check_redis_data():
    """检查Redis中的数据"""
    print("\n检查Redis中的现有数据...")
    
    try:
        session_redis = redis.Redis(host='127.0.0.1', port=6379, db=1)
        
        # 获取所有键
        keys = session_redis.keys('*')
        print(f"Session Redis (db=1) 中有 {len(keys)} 个键:")
        
        for key in keys[:10]:  # 只显示前10个
            value = session_redis.get(key)
            print(f"  {key.decode()} -> {value.decode() if value else 'None'}")
        
        if len(keys) > 10:
            print(f"  ... 还有 {len(keys) - 10} 个键")
            
    except Exception as e:
        print(f"检查Redis数据失败: {e}")

def main():
    # 先检查现有数据
    check_redis_data()
    
    # 设置测试token
    if setup_test_token():
        print("\n✅ 所有设置完成，可以开始测试了！")
    else:
        print("\n❌ 设置失败，请检查Redis连接")

if __name__ == '__main__':
    main()
