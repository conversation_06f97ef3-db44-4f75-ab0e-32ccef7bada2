
# 简单的stackless模拟模块
import threading
import queue
import time
import sys
import traceback

# 全局任务队列
_task_queue = queue.Queue()
_current_tasklet = None
_main_tasklet = None

class TaskletExit(Exception):
    pass

class bomb(Exception):
    """当tasklet被杀死时引发的异常"""
    def __init__(self, exc_type=None, exc_value=None, exc_traceback=None):
        self.exc_type = exc_type
        self.exc_value = exc_value
        self.exc_traceback = exc_traceback

class channel:
    def __init__(self):
        self.queue = queue.Queue()
        self.balance = 0
        self.preference = 0
        self.closing = False
    
    def send(self, value):
        """向通道发送值"""
        if self.closing:
            raise ValueError("Cannot send on a closed channel")
        self.balance += 1
        self.queue.put(value)
        schedule()
        return value
    
    def receive(self):
        """从通道接收值"""
        if self.closing and self.queue.empty():
            raise StopIteration("No more values in channel")
        self.balance -= 1
        value = self.queue.get()
        schedule()
        return value
    
    def close(self):
        """关闭通道"""
        self.closing = True

class tasklet:
    def __init__(self, func=None, *args, **kwargs):
        self.func = func
        self.args = args
        self.kwargs = kwargs
        self.alive = False
        self.blocked = False
        self.paused = False
        self.next = None
    
    def __call__(self, *args, **kwargs):
        """使tasklet可调用"""
        if args or kwargs:
            self.args = args
            self.kwargs = kwargs
        return self
    
    def setup(self, *args, **kwargs):
        """设置tasklet的参数"""
        if args or kwargs:
            self.args = args
            self.kwargs = kwargs
        return self
    
    def insert(self):
        """将tasklet插入调度队列"""
        global _task_queue
        self.alive = True
        if self.func:
            thread = threading.Thread(target=self.func, args=self.args, kwargs=self.kwargs)
            thread.daemon = True
            thread.start()
        return self
    
    def remove(self):
        """从调度队列中移除tasklet"""
        self.alive = False
        return self
    
    def kill(self):
        """杀死tasklet"""
        self.alive = False
        return self
    
    def raise_exception(self, exc_type, *args):
        """在tasklet中引发异常"""
        return self

def getcurrent():
    """获取当前运行的tasklet"""
    global _current_tasklet
    if _current_tasklet is None:
        # 如果没有当前tasklet，返回主tasklet
        global _main_tasklet
        if _main_tasklet is None:
            _main_tasklet = tasklet()
            _main_tasklet.alive = True
        return _main_tasklet
    return _current_tasklet

def schedule():
    """执行一次调度循环"""
    time.sleep(0.001)

def run():
    """运行调度器，直到没有更多的tasklet"""
    time.sleep(0.001)

def runcount(count):
    """运行调度器指定的次数"""
    time.sleep(0.001 * count)

# 创建主tasklet
_main_tasklet = tasklet()
_main_tasklet.alive = True
_current_tasklet = _main_tasklet
