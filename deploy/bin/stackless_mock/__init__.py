
# ģ��stacklessģ��
class bomb(Exception):
    pass

class tasklet(object):
    def __init__(self, func=None, *args, **kwargs):
        self.func = func
        self.args = args
        self.kwargs = kwargs
    
    def __call__(self, *args, **kwargs):
        return self
    
    def setup(self, *args, **kwargs):
        return self
    
    def insert(self):
        return self
    
    def remove(self):
        return self

def schedule(*args, **kwargs):
    pass

def run(*args, **kwargs):
    pass

def getcurrent(*args, **kwargs):
    return tasklet()
