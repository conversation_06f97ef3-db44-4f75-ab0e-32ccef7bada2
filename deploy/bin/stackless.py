"""
stackless模块的替代实现，使用eventlet作为后端
这个模块提供了一个简单的兼容层，使得依赖stackless的代码可以在标准Python中运行
"""

import eventlet
import sys
import threading
import weakref

# 初始化eventlet
eventlet.monkey_patch()

# 任务类
class tasklet(object):
    """使用eventlet实现的tasklet类"""
    
    def __init__(self, func=None, *args, **kwargs):
        self.func = func
        self.args = args
        self.kwargs = kwargs
        self._greenlet = None
        self.alive = False
        self.blocked = False
        self.scheduled = False
        
    def __call__(self, *args, **kwargs):
        """允许tasklet作为可调用对象"""
        if args or kwargs:
            self.args = args
            self.kwargs = kwargs
        return self
        
    def setup(self, func, *args, **kwargs):
        """设置tasklet的函数和参数"""
        self.func = func
        self.args = args
        self.kwargs = kwargs
        return self
        
    def run(self):
        """运行tasklet"""
        if self.func:
            try:
                self.func(*self.args, **self.kwargs)
            finally:
                self.alive = False
                self.scheduled = False
        
    def insert(self):
        """将tasklet插入调度队列"""
        self.scheduled = True
        return self
        
    def remove(self):
        """从调度队列中移除tasklet"""
        self.scheduled = False
        return self
        
    def bind(self, func):
        """绑定函数到tasklet"""
        self.func = func
        return self
        
    def switch(self):
        """切换到这个tasklet"""
        if not self._greenlet:
            self._greenlet = eventlet.spawn(self.run)
            self.alive = True
        return self
        
    def kill(self):
        """终止tasklet"""
        if self._greenlet:
            self._greenlet.kill()
            self.alive = False
            self.scheduled = False
        return self

# 通道类
class channel(object):
    """使用eventlet实现的channel类"""
    
    def __init__(self):
        self.queue = eventlet.queue.Queue()
        self.balance = 0
        
    def send(self, data):
        """向通道发送数据"""
        self.balance += 1
        self.queue.put(data)
        
    def receive(self):
        """从通道接收数据"""
        self.balance -= 1
        return self.queue.get()
        
    def send_throw(self, exc_type, exc_value=None, exc_tb=None):
        """发送异常"""
        self.balance += 1
        self.queue.put((exc_type, exc_value, exc_tb))
        
    def receive_throw(self):
        """接收可能的异常"""
        data = self.queue.get()
        if isinstance(data, tuple) and len(data) == 3 and issubclass(data[0], Exception):
            raise data[0](data[1]).with_traceback(data[2])
        return data

# 调度器类
class scheduler(object):
    """使用eventlet实现的调度器"""
    
    def __init__(self):
        self.runnable = []
        self.current = None
        
    def schedule(self, task=None):
        """调度一个任务"""
        if task:
            task.scheduled = True
            if not task._greenlet:
                task._greenlet = eventlet.spawn(task.run)
                task.alive = True
        eventlet.sleep(0)
        
    def schedule_remove(self, task):
        """移除并调度一个任务"""
        task.scheduled = False
        return self.schedule()
        
    def run(self):
        """运行调度器"""
        while self.runnable:
            eventlet.sleep(0.01)

# 全局实例
_main_tasklet = tasklet()
_main_tasklet.alive = True
_main_tasklet._greenlet = eventlet.getcurrent()

_scheduler = scheduler()

# 全局函数
def get_scheduler():
    """获取当前线程的调度器"""
    return _scheduler

def getcurrent():
    """获取当前运行的tasklet"""
    return _main_tasklet

def schedule():
    """让出执行权"""
    eventlet.sleep(0)

def schedule_remove(task=None):
    """移除任务并调度"""
    if task:
        task.scheduled = False
    eventlet.sleep(0)

def getruncount():
    """获取当前运行的tasklet数量"""
    # 简单返回1，表示主线程
    return 1

def run():
    """运行调度器直到所有tasklet完成"""
    eventlet.sleep(0)

# 异常类
class TaskletExit(SystemExit):
    """当tasklet被终止时抛出的异常"""
    pass

class bomb(Exception):
    """当tasklet被杀死时引发的异常"""
    def __init__(self, exc_type=None, exc_value=None, exc_traceback=None):
        self.exc_type = exc_type or Exception
        self.exc_value = exc_value
        self.exc_traceback = exc_traceback
        super().__init__(exc_value)

    def throw(self, tasklet=None):
        """在指定的tasklet中抛出异常"""
        if self.exc_value:
            raise self.exc_type(self.exc_value)
        else:
            raise self.exc_type()