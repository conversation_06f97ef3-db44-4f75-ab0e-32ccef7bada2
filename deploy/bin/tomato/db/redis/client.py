# -*- coding=utf-8 -*-
"""
修补版Redis客户端 - 使用同步连接
"""

import redis
from tomato.utils import ttlog

class MockRedisConnection:
    """模拟Redis连接对象"""
    def __init__(self, redis_client):
        self.client = redis_client
        self._client = redis_client  # 兼容性属性
        self.maxDelay = 3
        self._pubsub = None

    def send(self, *args):
        """发送命令"""
        try:
            if len(args) == 1:
                return self.client.execute_command(args[0])
            else:
                return self.client.execute_command(*args)
        except Exception as e:
            ttlog.error('Redis command failed:', args, 'error:', e)
            raise

    def subscribe(self, channel, callback):
        """订阅Redis频道"""
        try:
            if not self._pubsub:
                self._pubsub = self.client.pubsub()
            self._pubsub.subscribe(channel)
            ttlog.info('Subscribed to channel:', channel)
        except Exception as e:
            ttlog.error('Subscribe failed:', e)
            raise

    def stopTrying(self):
        """停止尝试连接"""
        pass

    def close(self):
        """关闭连接"""
        try:
            if self._pubsub:
                self._pubsub.close()
            self.client.close()
        except:
            pass

def connectRedis(host, port, db, password=None, timeout=30, maxDelay=3):
    """连接Redis - 同步版本"""
    ttlog.info('connectRedis',
               'host=', host,
               'port=', port,
               'db=', db,
               'password=', password)
    
    try:
        # 使用标准redis库
        client = redis.Redis(
            host=host,
            port=port,
            db=db,
            password=password or None,
            socket_timeout=timeout,
            socket_connect_timeout=timeout
        )
        
        # 测试连接
        client.ping()
        
        # 返回模拟的连接对象
        conn = MockRedisConnection(client)
        return conn
        
    except Exception as e:
        ttlog.error('connectRedis failed:', e)
        raise

def connectRedisCluster(confList, timeout, maxDelay):
    """连接Redis集群 - 同步版本"""
    connList = []
    try:
        for conf in confList:
            conn = connectRedis(conf['host'], conf['port'], conf['db'], conf.get('password'), timeout, maxDelay)
            connList.append(conn)
        return connList
    except:
        for conn in connList:
            closeRedis(conn)
        raise

def connectSubscriber(host, port, db, password=None, timeout=30):
    """连接Redis订阅者 - 同步版本"""
    ttlog.info('connectSubscriber',
               'host=', host,
               'port=', port,
               'db=', db,
               'password=', password)

    try:
        # 使用标准redis库
        client = redis.Redis(
            host=host,
            port=port,
            db=db,
            password=password or None,
            socket_timeout=timeout,
            socket_connect_timeout=timeout
        )

        # 测试连接
        client.ping()

        # 返回模拟的连接对象
        conn = MockRedisConnection(client)
        return conn

    except Exception as e:
        ttlog.error('connectSubscriber failed:', e)
        raise

def closeRedis(conn):
    """关闭Redis连接"""
    try:
        if hasattr(conn, 'client'):
            conn.client.close()
    except:
        pass

# 保持其他类和函数的兼容性
class TTRedisClientFactory:
    def __init__(self, *args, **kwargs):
        pass

class TTRedisPool:
    def __init__(self):
        self._redis = {}
        self._subscribers = {}
    
    def getRedis(self, host, port, db, timeout):
        key = (host, port, db)
        r = self._redis.get(key)
        if not r:
            r = connectRedis(host, port, db, timeout=timeout)
            self._redis[key] = r
        return r
