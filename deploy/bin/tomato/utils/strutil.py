# -*- coding=utf-8 -*-
'''
Created on 2017年11月20日

@author: zhaojiangang
'''
import codecs
from hashlib import md5
import json

import msgpack


def md5Digest(s, lower=True):
    '''
    计算一个字符串的MD5值O
    '''
    m = md5()
    m.update(s)
    ret = m.hexdigest()
    if lower:
        return ret.lower()
    return ret


def jsonLoads(jstr, encoding='utf8'):
    # Python 3兼容性：json.loads不再需要encoding参数
    if isinstance(jstr, bytes):
        jstr = jstr.decode(encoding)
    return json.loads(jstr)


def jsonLoadFile(filePath, encoding='utf8'):
    with codecs.open(filePath, 'r', encoding) as f:
        # Python 3兼容性：json.load不再需要encoding参数
        return json.load(f)


def jsonDumps(obj):
    return json.dumps(obj, separators=(',', ':'))


def msgpackLoads(obj, **kw):
    return msgpack.loads(obj, **kw)


def msgpackDumps(obj, **kw):
    return msgpack.dumps(obj, **kw)


