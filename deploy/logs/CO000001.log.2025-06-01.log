2025-06-01 22:41:25,128 - CO000001 - DEBUG - 添加stackless模拟路径: I:\live_room_service-master\live_room_service-master\deploy\bin\stackless_simple
2025-06-01 22:41:25,129 - CO000001 - DEBUG - 添加bin路径: I:\live_room_service-master\live_room_service-master\deploy\bin
2025-06-01 22:41:25,130 - CO000001 - DEBUG - 开始导入server模块
2025-06-01 22:41:25,822 - CO000001 - ERROR - 启动过程中出错: cannot import name 'bomb' from 'stackless' (I:\live_room_service-master\live_room_service-master\deploy\bin\stackless.py)
2025-06-01 22:41:25,824 - CO000001 - ERROR - Traceback (most recent call last):
  File "I:\live_room_service-master\live_room_service-master\start_CO000001_debug.py", line 27, in <module>
    from tomato.server import server
  File "I:\live_room_service-master\live_room_service-master\deploy\bin\tomato\__init__.py", line 7, in <module>
    from tomato.application import TTApplication
  File "I:\live_room_service-master\live_room_service-master\deploy\bin\tomato\application.py", line 7, in <module>
    from tomato.agent.base import TTAgentDelegate, TTAgentMsgTypes, TTAgent
  File "I:\live_room_service-master\live_room_service-master\deploy\bin\tomato\agent\base.py", line 11, in <module>
    from tomato.core.tasklet import TTTasklet
  File "I:\live_room_service-master\live_room_service-master\deploy\bin\tomato\core\tasklet.py", line 13, in <module>
    from tomato.core.future import TTFuture
  File "I:\live_room_service-master\live_room_service-master\deploy\bin\tomato\core\future.py", line 7, in <module>
    from stackless import bomb
ImportError: cannot import name 'bomb' from 'stackless' (I:\live_room_service-master\live_room_service-master\deploy\bin\stackless.py)

2025-06-01 22:45:14,261 - CO000001 - DEBUG - 添加stackless模拟路径: I:\live_room_service-master\live_room_service-master\deploy\bin\stackless_complete
2025-06-01 22:45:14,262 - CO000001 - DEBUG - 添加bin路径: I:\live_room_service-master\live_room_service-master\deploy\bin
2025-06-01 22:45:14,263 - CO000001 - DEBUG - 开始导入server模块
2025-06-01 22:45:15,430 - CO000001 - ERROR - 启动过程中出错: cannot import name 'bomb' from 'stackless' (I:\live_room_service-master\live_room_service-master\deploy\bin\stackless.py)
2025-06-01 22:45:15,431 - CO000001 - ERROR - Traceback (most recent call last):
  File "I:\live_room_service-master\live_room_service-master\start_CO000001.py", line 26, in <module>
    from tomato.server import server
  File "I:\live_room_service-master\live_room_service-master\deploy\bin\tomato\__init__.py", line 7, in <module>
    from tomato.application import TTApplication
  File "I:\live_room_service-master\live_room_service-master\deploy\bin\tomato\application.py", line 7, in <module>
    from tomato.agent.base import TTAgentDelegate, TTAgentMsgTypes, TTAgent
  File "I:\live_room_service-master\live_room_service-master\deploy\bin\tomato\agent\base.py", line 11, in <module>
    from tomato.core.tasklet import TTTasklet
  File "I:\live_room_service-master\live_room_service-master\deploy\bin\tomato\core\tasklet.py", line 13, in <module>
    from tomato.core.future import TTFuture
  File "I:\live_room_service-master\live_room_service-master\deploy\bin\tomato\core\future.py", line 7, in <module>
    from stackless import bomb
ImportError: cannot import name 'bomb' from 'stackless' (I:\live_room_service-master\live_room_service-master\deploy\bin\stackless.py)

2025-06-01 22:47:57,872 - CO000001 - DEBUG - 添加stackless模拟路径: I:\live_room_service-master\live_room_service-master\deploy\bin\stackless_simple
2025-06-01 22:47:57,873 - CO000001 - DEBUG - 添加bin路径: I:\live_room_service-master\live_room_service-master\deploy\bin
2025-06-01 22:47:57,873 - CO000001 - DEBUG - 开始导入server模块
2025-06-01 22:47:58,488 - CO000001 - ERROR - 启动过程中出错: cannot import name 'bomb' from 'stackless' (I:\live_room_service-master\live_room_service-master\deploy\bin\stackless.py)
2025-06-01 22:47:58,489 - CO000001 - ERROR - Traceback (most recent call last):
  File "I:\live_room_service-master\live_room_service-master\start_CO000001_debug.py", line 27, in <module>
    from tomato.server import server
  File "I:\live_room_service-master\live_room_service-master\deploy\bin\tomato\__init__.py", line 7, in <module>
    from tomato.application import TTApplication
  File "I:\live_room_service-master\live_room_service-master\deploy\bin\tomato\application.py", line 7, in <module>
    from tomato.agent.base import TTAgentDelegate, TTAgentMsgTypes, TTAgent
  File "I:\live_room_service-master\live_room_service-master\deploy\bin\tomato\agent\base.py", line 11, in <module>
    from tomato.core.tasklet import TTTasklet
  File "I:\live_room_service-master\live_room_service-master\deploy\bin\tomato\core\tasklet.py", line 13, in <module>
    from tomato.core.future import TTFuture
  File "I:\live_room_service-master\live_room_service-master\deploy\bin\tomato\core\future.py", line 7, in <module>
    from stackless import bomb
ImportError: cannot import name 'bomb' from 'stackless' (I:\live_room_service-master\live_room_service-master\deploy\bin\stackless.py)

