#!/usr/bin/env python
# -*- coding=utf-8 -*-
'''
启动服务并显示详细日志的脚本
'''

import os
import sys
import subprocess
import time
import threading

def main():
    if len(sys.argv) < 2:
        print("用法: python start_service_with_logs.py <服务ID>")
        return
    
    service_id = sys.argv[1]
    
    # 项目路径配置
    current_dir = os.path.dirname(os.path.abspath(__file__))
    bin_dir = os.path.join(current_dir, 'deploy', 'bin')
    config_path = os.path.join(current_dir, 'deploy', 'config')
    logs_path = os.path.join(current_dir, 'deploy', 'logs')
    run_py = os.path.join(bin_dir, 'tomato', 'run.py')
    
    # 确保日志目录存在
    os.makedirs(logs_path, exist_ok=True)
    
    # 设置环境变量
    env = dict(os.environ)
    env["PYTHONPATH"] = f"{current_dir};{bin_dir}"
    
    cmd = [
        sys.executable,
        run_py,
        service_id,
        'fqparty',
        config_path,
        logs_path,
        '0'
    ]
    
    print(f"启动服务 {service_id}...")
    print(f"命令: {' '.join(cmd)}")
    print("=" * 60)
    
    # 启动进程并捕获输出
    process = subprocess.Popen(
        cmd,
        env=env,
        cwd=current_dir,
        stdout=subprocess.PIPE,
        stderr=subprocess.STDOUT,
        universal_newlines=True,
        bufsize=1
    )
    
    # 实时显示输出
    def read_output():
        try:
            for line in iter(process.stdout.readline, ''):
                if line:
                    print(f"[{service_id}] {line.strip()}")
        except:
            pass
    
    # 启动输出读取线程
    output_thread = threading.Thread(target=read_output, daemon=True)
    output_thread.start()
    
    # 同时监控日志文件
    log_file = os.path.join(logs_path, f"{service_id}.log")
    
    def monitor_log():
        last_size = 0
        while process.poll() is None:
            try:
                if os.path.exists(log_file):
                    current_size = os.path.getsize(log_file)
                    if current_size > last_size:
                        with open(log_file, 'r', encoding='utf-8') as f:
                            f.seek(last_size)
                            new_content = f.read()
                            if new_content.strip():
                                print(f"[LOG] {new_content.strip()}")
                        last_size = current_size
                time.sleep(0.5)
            except:
                pass
    
    # 启动日志监控线程
    log_thread = threading.Thread(target=monitor_log, daemon=True)
    log_thread.start()
    
    try:
        # 等待进程结束
        return_code = process.wait()
        print(f"\n服务 {service_id} 退出，返回码: {return_code}")
        
        # 显示最终日志
        if os.path.exists(log_file):
            print(f"\n最终日志内容 ({log_file}):")
            print("-" * 60)
            with open(log_file, 'r', encoding='utf-8') as f:
                content = f.read()
                if content.strip():
                    print(content)
                else:
                    print("日志文件为空")
        
    except KeyboardInterrupt:
        print(f"\n停止服务 {service_id}...")
        process.terminate()
        process.wait()

if __name__ == '__main__':
    main()
