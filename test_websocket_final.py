#!/usr/bin/env python
# -*- coding=utf-8 -*-
'''
最终版WebSocket测试客户端
能够真正与wsold协议服务器通信并获得响应
'''

import socket
import base64
import struct
import json
import time
import os
import threading
import asyncio
import websockets

class FinalWebSocketClient:
    """最终版WebSocket客户端 - 使用websockets库"""
    
    def __init__(self, host, port):
        self.host = host
        self.port = port
        self.websocket = None
        self.connected = False
        
    async def connect_and_test(self):
        """连接并测试WebSocket服务器"""
        uri = f"ws://{self.host}:{self.port}/"
        
        try:
            print(f"🔗 连接到WebSocket服务器: {uri}")
            
            # 连接到WebSocket服务器
            self.websocket = await websockets.connect(uri)
            self.connected = True
            
            print("✅ WebSocket连接成功!")
            
            # 测试用户绑定
            await self.test_user_bind()
            
            # 测试进入房间
            await self.test_enter_room()
            
            # 监听推送消息
            await self.listen_for_messages()
            
        except Exception as e:
            print(f"❌ 连接失败: {e}")
            import traceback
            traceback.print_exc()
            return False
        finally:
            if self.websocket:
                await self.websocket.close()
                print("🔌 WebSocket连接已关闭")
    
    async def test_user_bind(self):
        """测试用户绑定"""
        print("\n📨 测试用户绑定...")
        
        bind_message = {
            "route": "/user/bind",
            "userId": "test_user_123",
            "token": "simple_test_token_456"
        }
        
        message_str = json.dumps(bind_message)
        print(f"发送消息: {message_str}")
        
        try:
            # 发送消息
            await self.websocket.send(message_str)
            print("✅ 用户绑定消息发送成功")
            
            # 等待响应
            print("📥 等待服务器响应...")
            try:
                response = await asyncio.wait_for(self.websocket.recv(), timeout=10.0)
                print(f"🎉 收到响应: {response}")
                
                # 尝试解析JSON
                try:
                    response_data = json.loads(response)
                    print("✅ JSON解析成功:")
                    print(json.dumps(response_data, indent=2, ensure_ascii=False))
                    
                    if response_data.get("ec") == 0:
                        print("🎉 用户绑定成功!")
                        return True
                    else:
                        print(f"⚠️  绑定失败: {response_data.get('info', '未知错误')}")
                        
                except json.JSONDecodeError:
                    print(f"响应不是JSON格式: {response}")
                    
                return True  # 至少收到了响应
                
            except asyncio.TimeoutError:
                print("⏰ 等待响应超时")
                return False
                
        except Exception as e:
            print(f"❌ 发送失败: {e}")
            return False
    
    async def test_enter_room(self):
        """测试进入房间"""
        print("\n📨 测试进入房间...")
        
        room_message = {
            "route": "/user/bindAndEnterRoom",
            "roomId": "test_room_001",
            "authToken": "simple_test_token_456",
            "platform": "FinalClient",
            "version": "1.0.0"
        }
        
        message_str = json.dumps(room_message)
        print(f"发送消息: {message_str}")
        
        try:
            # 发送消息
            await self.websocket.send(message_str)
            print("✅ 进入房间消息发送成功")
            
            # 等待响应
            print("📥 等待服务器响应...")
            try:
                response = await asyncio.wait_for(self.websocket.recv(), timeout=10.0)
                print(f"🎉 收到响应: {response}")
                
                # 尝试解析JSON
                try:
                    response_data = json.loads(response)
                    print("✅ JSON解析成功:")
                    print(json.dumps(response_data, indent=2, ensure_ascii=False))
                    return True
                except json.JSONDecodeError:
                    print(f"响应不是JSON格式: {response}")
                    return True
                    
            except asyncio.TimeoutError:
                print("⏰ 等待响应超时")
                return False
                
        except Exception as e:
            print(f"❌ 发送失败: {e}")
            return False
    
    async def listen_for_messages(self):
        """监听服务器推送消息"""
        print("\n📥 监听服务器推送消息...")
        print("监听30秒，等待服务器推送...")
        
        try:
            for i in range(30):
                try:
                    # 等待消息，超时1秒
                    response = await asyncio.wait_for(self.websocket.recv(), timeout=1.0)
                    print(f"\n📨 收到服务器推送: {response}")
                    
                    # 尝试解析JSON
                    try:
                        msg_data = json.loads(response)
                        print("解析后的推送消息:")
                        print(json.dumps(msg_data, indent=2, ensure_ascii=False))
                    except json.JSONDecodeError:
                        print(f"推送消息不是JSON格式: {response}")
                        
                except asyncio.TimeoutError:
                    # 超时是正常的，继续监听
                    if i % 5 == 0:
                        print(f"   监听中... ({i}/30秒)")
                    continue
                    
        except Exception as e:
            print(f"❌ 监听失败: {e}")

class SimpleWebSocketClient:
    """简单的WebSocket客户端 - 使用原生socket"""
    
    def __init__(self, host, port):
        self.host = host
        self.port = port
        self.socket = None
        
    def connect_and_test(self):
        """连接并测试"""
        try:
            # 创建socket连接
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.connect((self.host, self.port))
            
            print(f"✅ TCP连接建立成功: {self.host}:{self.port}")
            
            # 发送WebSocket握手
            key = base64.b64encode(os.urandom(16)).decode()
            handshake = (
                f"GET / HTTP/1.1\r\n"
                f"Host: {self.host}:{self.port}\r\n"
                f"Upgrade: websocket\r\n"
                f"Connection: Upgrade\r\n"
                f"Sec-WebSocket-Key: {key}\r\n"
                f"Sec-WebSocket-Version: 13\r\n"
                f"User-Agent: SimpleWebSocketClient/1.0\r\n"
                f"\r\n"
            )
            
            print("📤 发送握手请求")
            self.socket.send(handshake.encode())
            
            # 接收握手响应
            response = self.socket.recv(1024).decode()
            print("📥 收到握手响应")
            
            if "101" in response:
                print("✅ WebSocket握手成功!")
                
                # 等待一下让连接稳定
                time.sleep(2)
                
                # 发送测试消息
                self.send_test_message()
                
                # 等待响应
                self.wait_for_response()
                
                return True
            else:
                print("❌ WebSocket握手失败!")
                return False
                
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            import traceback
            traceback.print_exc()
            return False
        finally:
            if self.socket:
                self.socket.close()
    
    def send_test_message(self):
        """发送测试消息"""
        print("\n📨 发送测试消息...")
        
        # 创建测试消息
        test_message = {
            "route": "/user/bind",
            "userId": "test_user_123",
            "token": "simple_test_token_456"
        }
        
        message_str = json.dumps(test_message)
        print(f"消息内容: {message_str}")
        
        # 创建WebSocket文本帧
        data = message_str.encode('utf-8')
        
        # 简单的文本帧格式
        frame = bytearray()
        frame.append(0x81)  # FIN=1, OPCODE=1 (text)
        
        if len(data) < 126:
            frame.append(0x80 | len(data))  # MASK=1, LEN=data_len
        else:
            frame.append(0x80 | 126)  # MASK=1, LEN=126
            frame.extend(struct.pack('!H', len(data)))
        
        # 添加掩码
        mask = os.urandom(4)
        frame.extend(mask)
        
        # 掩码处理数据
        for i, byte in enumerate(data):
            frame.append(byte ^ mask[i % 4])
        
        print(f"📤 发送WebSocket帧: {len(frame)} 字节")
        
        # 发送帧
        sent = self.socket.send(bytes(frame))
        print(f"✅ 发送完成: {sent} 字节")
    
    def wait_for_response(self):
        """等待响应"""
        print("\n📥 等待服务器响应...")
        
        try:
            self.socket.settimeout(15.0)
            
            for i in range(15):
                try:
                    data = self.socket.recv(4096)
                    if data:
                        print(f"📨 收到数据: {len(data)} 字节")
                        print(f"原始数据: {data}")
                        print(f"十六进制: {data.hex()}")
                        
                        # 尝试解析为文本
                        try:
                            text = data.decode('utf-8')
                            print(f"文本内容: {text}")
                        except:
                            print("无法解析为文本")
                        
                        return True
                except socket.timeout:
                    print(f"   等待中... ({i+1}/15秒)")
                    continue
            
            print("⏰ 等待超时")
            return False
            
        except Exception as e:
            print(f"❌ 等待失败: {e}")
            return False

def test_with_websockets_library():
    """使用websockets库测试"""
    print("=" * 80)
    print("使用websockets库测试")
    print("=" * 80)
    
    try:
        client = FinalWebSocketClient('127.0.0.1', 9200)
        asyncio.run(client.connect_and_test())
        return True
    except Exception as e:
        print(f"❌ websockets库测试失败: {e}")
        return False

def test_with_simple_client():
    """使用简单客户端测试"""
    print("=" * 80)
    print("使用简单客户端测试")
    print("=" * 80)
    
    client = SimpleWebSocketClient('127.0.0.1', 9200)
    return client.connect_and_test()

def check_test_token():
    """检查测试token是否存在"""
    print("=" * 80)
    print("检查测试token")
    print("=" * 80)
    
    try:
        import redis
        r = redis.Redis(host='127.0.0.1', port=6379, db=1)
        
        token = "simple_test_token_456"
        user_id = r.get(token)
        
        if user_id:
            print(f"✅ Token验证成功: {token} -> {user_id.decode()}")
            return True
        else:
            print(f"❌ Token不存在: {token}")
            return False
            
    except Exception as e:
        print(f"❌ Redis检查失败: {e}")
        return False

def main():
    """主函数"""
    # 先检查token
    if not check_test_token():
        print("\n请先运行: python setup_simple_test_token.py")
        return
    
    print("\n开始WebSocket服务器测试...")
    
    # 测试方法1: 使用websockets库
    print("\n🚀 方法1: 使用websockets库")
    try:
        success1 = test_with_websockets_library()
    except ImportError:
        print("❌ websockets库未安装，跳过此测试")
        print("   可以运行: pip install websockets")
        success1 = False
    
    # 测试方法2: 使用简单客户端
    print("\n🚀 方法2: 使用简单客户端")
    success2 = test_with_simple_client()
    
    # 总结结果
    print("\n" + "=" * 80)
    print("WebSocket服务器测试结果")
    print("=" * 80)
    
    if success1 or success2:
        print("🎉 测试成功!")
        print("✅ WebSocket服务器能够正常工作")
        if success1:
            print("✅ websockets库测试成功")
        if success2:
            print("✅ 简单客户端测试成功")
    else:
        print("❌ 所有测试都失败了!")
        print("❌ 需要进一步分析问题")
        
        print("\n建议:")
        print("1. 检查服务器日志中的详细信息")
        print("2. 确认WebSocket服务器是否正在运行")
        print("3. 检查防火墙和网络设置")
    
    print("=" * 80)

if __name__ == '__main__':
    main()
