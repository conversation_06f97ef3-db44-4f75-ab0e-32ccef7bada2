#!/usr/bin/env python
# -*- coding=utf-8 -*-
'''
在当前终端启动所有直播间服务的脚本
'''

import os
import sys
import subprocess
import time
import threading
import signal

def main():
    print("=" * 60)
    print("启动所有直播间服务 (终端模式)")
    print("=" * 60)
    
    # 项目路径配置
    current_dir = os.path.dirname(os.path.abspath(__file__))
    bin_dir = os.path.join(current_dir, 'deploy', 'bin')
    config_path = os.path.join(current_dir, 'deploy', 'config')
    logs_path = os.path.join(current_dir, 'deploy', 'logs')
    run_py = os.path.join(bin_dir, 'tomato', 'run.py')
    
    # 确保日志目录存在
    os.makedirs(logs_path, exist_ok=True)
    
    # 服务列表
    services = [
        ('CO000001', '连接服务器'),
        ('US000001', '用户服务器'),
        ('HT000001', 'HTTP服务器'),
        ('RM000001', '房间服务器')
    ]
    
    # 设置环境变量
    env = dict(os.environ)
    env["PYTHONPATH"] = f"{current_dir};{bin_dir}"
    
    processes = []
    
    def signal_handler(sig, frame):
        print("\n\n收到停止信号，正在关闭所有服务...")
        for service_id, service_name, process in processes:
            if process and process.poll() is None:
                print(f"停止 {service_name}...")
                process.terminate()
        sys.exit(0)
    
    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    
    print("开始启动服务...")
    print("-" * 40)
    
    for service_id, service_name in services:
        print(f"\n启动 {service_name} ({service_id})...")
        
        cmd = [
            sys.executable,
            run_py,
            service_id,
            'fqparty',
            config_path,
            logs_path,
            '0'
        ]
        
        try:
            # 在后台启动服务，但输出到当前终端
            process = subprocess.Popen(
                cmd,
                env=env,
                cwd=current_dir,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                bufsize=1
            )
            
            processes.append((service_id, service_name, process))
            print(f"  ✅ {service_name} 启动成功 (PID: {process.pid})")
            
            # 等待一下看是否有立即的错误
            time.sleep(1)
            if process.poll() is not None:
                print(f"  ❌ {service_name} 启动失败，进程已退出")
                # 读取错误输出
                output, _ = process.communicate()
                if output:
                    print(f"  错误信息: {output}")
            
        except Exception as e:
            print(f"  ❌ {service_name} 启动失败: {e}")
    
    if processes:
        print("\n" + "=" * 60)
        print("服务启动完成！")
        print("=" * 60)
        
        # 检查哪些服务还在运行
        running_services = []
        for service_id, service_name, process in processes:
            if process and process.poll() is None:
                running_services.append((service_id, service_name, process))
        
        if running_services:
            print("\n运行中的服务:")
            for service_id, service_name, process in running_services:
                if service_id == 'HT000001':
                    print(f"  🌐 {service_name}: http://127.0.0.1:9000/ (PID: {process.pid})")
                elif service_id == 'CO000001':
                    print(f"  🔌 {service_name}: ws://127.0.0.1:9200/ (PID: {process.pid})")
                else:
                    print(f"  ⚙️  {service_name}: 运行中 (PID: {process.pid})")
            
            print(f"\n📁 日志目录: {logs_path}")
            print("💡 按 Ctrl+C 停止所有服务")
            print("=" * 60)
            
            # 监控服务输出
            def monitor_service(service_id, service_name, process):
                try:
                    for line in iter(process.stdout.readline, ''):
                        if line:
                            print(f"[{service_id}] {line.strip()}")
                except:
                    pass
            
            # 为每个服务创建监控线程
            threads = []
            for service_id, service_name, process in running_services:
                thread = threading.Thread(
                    target=monitor_service, 
                    args=(service_id, service_name, process),
                    daemon=True
                )
                thread.start()
                threads.append(thread)
            
            try:
                # 等待所有进程结束
                while any(p.poll() is None for _, _, p in running_services):
                    time.sleep(1)
            except KeyboardInterrupt:
                print("\n\n收到停止信号，正在关闭所有服务...")
                for service_id, service_name, process in running_services:
                    if process.poll() is None:
                        print(f"停止 {service_name}...")
                        process.terminate()
                        
        else:
            print("\n❌ 没有服务成功启动")
            print("请检查日志文件获取详细错误信息")
    else:
        print("\n❌ 没有服务成功启动")
    
    print("\n服务已停止")

if __name__ == '__main__':
    main()
