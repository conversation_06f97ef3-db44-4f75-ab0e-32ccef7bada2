#!/usr/bin/env python
# -*- coding=utf-8 -*-
import os
import sys

# 确保标准库路径优先
import importlib.util
import types

# 移除可能冲突的路径
paths_to_remove = []
for path in sys.path:
    if 'tomato' in path.lower() and 'http' in os.listdir(path) if os.path.isdir(path) else False:
        paths_to_remove.append(path)

for path in paths_to_remove:
    if path in sys.path:
        sys.path.remove(path)

# 确保标准库在最前面
import http.cookiejar  # 测试标准库导入

# 现在添加项目路径
current_dir = r"I:\live_room_service-master\live_room_service-master"
bin_dir = os.path.join(current_dir, "deploy", "bin")
config_path = os.path.join(current_dir, "deploy", "config")
logs_path = os.path.join(current_dir, "deploy", "logs")

# 添加到路径末尾
sys.path.append(bin_dir)

# 设置环境变量
os.environ["PYTHONPATH"] = bin_dir

# 导入并运行
try:
    from tomato.server import server
    import fqparty
    
    service_id = sys.argv[1] if len(sys.argv) > 1 else "CO000001"
    print(f"启动服务: {service_id}")
    
    server.runWithFileConf(service_id, config_path, fqparty.app, [])
except Exception as e:
    print(f"启动失败: {e}")
    import traceback
    traceback.print_exc()
    input("按回车键退出...")
