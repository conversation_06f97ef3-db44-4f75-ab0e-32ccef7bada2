
import sys
import os
import traceback

# ���ø���ϸ����־
import logging
logging.basicConfig(level=logging.DEBUG, 
                   format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('debug_http')

try:
    # ���ģ���stacklessģ�鵽·��
    logger.debug("���·��: %s", r"I:\live_room_service-master\live_room_service-master\deploy\bin\stackless_mock")
    sys.path.insert(0, r"I:\live_room_service-master\live_room_service-master\deploy\bin\stackless_mock")
    
    logger.debug("���·��: %s", r"I:\live_room_service-master\live_room_service-master\deploy\bin")
    sys.path.insert(1, r"I:\live_room_service-master\live_room_service-master\deploy\bin")
    
    # �����Ҫ��ģ��
    logger.debug("��ʼ����serverģ��")
    from tomato.server import server
    logger.debug("�ɹ�����serverģ��")
    
    # �������·��
    logger.debug("����·��: %s", r"I:\live_room_service-master\live_room_service-master\deploy\config")
    if not os.path.exists(r"I:\live_room_service-master\live_room_service-master\deploy\config"):
        logger.error("����·��������!")
    else:
        logger.debug("����·�����ڣ�����: %s", os.listdir(r"I:\live_room_service-master\live_room_service-master\deploy\config"))
    
    # ������������
    server_config = os.path.join(r"I:\live_room_service-master\live_room_service-master\deploy\config", "server/tomato/servers.json")
    if os.path.exists(server_config):
        with open(server_config, 'r', encoding='utf-8') as f:
            logger.debug("����������: %s", f.read())
    
    # ���Redis����
    try:
        import socket
        s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        s.settimeout(1)
        s.connect(('127.0.0.1', 6379))
        s.close()
        logger.debug("Redis���ӳɹ�")
    except:
        logger.error("Redis����ʧ��!")
    
    # ���з���
    logger.debug("��ʼ����HTTP����: HT000001")
    server.runWithFileConf("HT000001", r"I:\live_room_service-master\live_room_service-master\deploy\config", __import__("fqparty").app, [])
except Exception as e:
    logger.error("���������г���: %s", str(e))
    logger.error(traceback.format_exc())
    input("���س����˳�...")
