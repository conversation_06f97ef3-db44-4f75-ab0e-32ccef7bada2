#!/usr/bin/env python
# -*- coding=utf-8 -*-
'''
生产环境启动脚本 - 适用于宝塔面板部署
'''

import os
import sys
import time
import signal
import logging
from pathlib import Path

def setup_environment():
    """设置环境变量和Python路径"""
    # 获取项目根目录
    project_root = Path(__file__).parent.absolute()
    
    # 设置Python路径
    python_paths = [
        str(project_root / "deploy" / "bin"),
        str(project_root / "fqparty-py" / "src"),
        str(project_root / "tomato-py" / "src"),
    ]
    
    for path in python_paths:
        if path not in sys.path:
            sys.path.insert(0, path)
    
    # 设置环境变量
    os.environ['PYTHONPATH'] = ':'.join(python_paths)
    
    print(f"✅ 项目根目录: {project_root}")
    print(f"✅ Python路径已设置: {len(python_paths)} 个路径")

def setup_logging():
    """设置日志"""
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_dir / "production.log"),
            logging.StreamHandler(sys.stdout)
        ]
    )
    
    logger = logging.getLogger(__name__)
    logger.info("生产环境日志系统已启动")
    return logger

def check_dependencies():
    """检查依赖"""
    logger = logging.getLogger(__name__)
    
    try:
        import redis
        import twisted
        logger.info("✅ 核心依赖检查通过")
        return True
    except ImportError as e:
        logger.error(f"❌ 依赖检查失败: {e}")
        return False

def check_redis_connection():
    """检查Redis连接"""
    logger = logging.getLogger(__name__)
    
    try:
        import redis
        r = redis.Redis(host='127.0.0.1', port=6379, db=0)
        r.ping()
        logger.info("✅ Redis连接正常")
        return True
    except Exception as e:
        logger.error(f"❌ Redis连接失败: {e}")
        return False

def start_websocket_service():
    """启动WebSocket连接服务"""
    logger = logging.getLogger(__name__)
    
    try:
        logger.info("🚀 启动WebSocket连接服务...")
        
        # 导入必要模块
        from tomato.app import TTApp
        from tomato.utils import configure
        from fqparty.app import Application
        
        # 初始化配置
        config_path = os.path.join(os.path.dirname(__file__), "deploy", "config")
        configure.initByFile(config_path)
        logger.info("✅ 配置初始化完成")
        
        # 设置日志级别
        import tomato.utils.log as ttlog
        ttlog.setLevel(ttlog.LOG_LEVEL_INFO)  # 生产环境使用INFO级别
        logger.info("✅ 日志级别设置完成")
        
        # 初始化tomato应用
        TTApp.init()
        logger.info("✅ tomato应用初始化完成")
        
        # 初始化fqparty应用
        Application.init()
        logger.info("✅ fqparty应用初始化完成")
        
        # 启动fqparty应用
        Application.start()
        logger.info("✅ fqparty应用启动完成")
        
        # 启动tomato应用
        TTApp.start()
        logger.info("✅ tomato应用启动完成")
        
        logger.info("🎉 WebSocket连接服务启动成功!")
        logger.info("📋 服务信息:")
        logger.info("   - WebSocket地址: ws://0.0.0.0:9200/")
        logger.info("   - 协议: wsold")
        logger.info("   - 最大连接数: 5000")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ WebSocket服务启动失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def signal_handler(signum, frame):
    """信号处理器"""
    logger = logging.getLogger(__name__)
    logger.info(f"收到信号 {signum}，正在优雅关闭服务...")
    
    try:
        # 停止reactor
        from twisted.internet import reactor
        if reactor.running:
            reactor.stop()
        logger.info("✅ 服务已优雅关闭")
    except Exception as e:
        logger.error(f"关闭服务时出错: {e}")
    
    sys.exit(0)

def main():
    """主函数"""
    print("=" * 80)
    print("live_room_service 生产环境启动")
    print("=" * 80)
    
    # 设置信号处理
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # 设置环境
    setup_environment()
    
    # 设置日志
    logger = setup_logging()
    
    # 检查依赖
    if not check_dependencies():
        logger.error("依赖检查失败，退出")
        sys.exit(1)
    
    # 检查Redis连接
    if not check_redis_connection():
        logger.error("Redis连接失败，退出")
        sys.exit(1)
    
    # 启动WebSocket服务
    if not start_websocket_service():
        logger.error("WebSocket服务启动失败，退出")
        sys.exit(1)
    
    logger.info("=" * 80)
    logger.info("🎉 生产环境启动完成!")
    logger.info("=" * 80)
    logger.info("服务状态:")
    logger.info("  ✅ WebSocket连接服务: 运行中")
    logger.info("  ✅ Redis数据库: 连接正常")
    logger.info("  ✅ 用户认证系统: 就绪")
    logger.info("  ✅ 消息路由系统: 就绪")
    logger.info("=" * 80)
    logger.info("按 Ctrl+C 停止服务")
    logger.info("=" * 80)
    
    try:
        # 启动Twisted reactor
        from twisted.internet import reactor
        reactor.run()
    except KeyboardInterrupt:
        logger.info("收到中断信号，正在关闭...")
    except Exception as e:
        logger.error(f"运行时错误: {e}")
        import traceback
        logger.error(traceback.format_exc())
    finally:
        logger.info("服务已停止")

if __name__ == '__main__':
    main()
