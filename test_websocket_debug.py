#!/usr/bin/env python
# -*- coding=utf-8 -*-
'''
调试版WebSocket客户端 - 详细分析数据传输
'''

import socket
import base64
import hashlib
import struct
import json
import time
import os
import threading

class DebugWebSocketClient:
    """调试版WebSocket客户端"""
    
    def __init__(self, host, port):
        self.host = host
        self.port = port
        self.socket = None
        self.connected = False
        
    def connect(self):
        """连接到WebSocket服务器"""
        try:
            # 创建socket连接
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.connect((self.host, self.port))
            
            print(f"✅ TCP连接建立成功: {self.host}:{self.port}")
            
            # 发送WebSocket握手请求
            key = base64.b64encode(os.urandom(16)).decode()
            
            handshake = (
                f"GET / HTTP/1.1\r\n"
                f"Host: {self.host}:{self.port}\r\n"
                f"Upgrade: websocket\r\n"
                f"Connection: Upgrade\r\n"
                f"Sec-WebSocket-Key: {key}\r\n"
                f"Sec-WebSocket-Version: 13\r\n"
                f"User-Agent: DebugWebSocketClient/1.0\r\n"
                f"\r\n"
            )
            
            print(f"📤 发送握手请求 ({len(handshake)} 字节):")
            print(handshake)
            
            sent_bytes = self.socket.send(handshake.encode())
            print(f"✅ 握手请求发送成功: {sent_bytes} 字节")
            
            # 接收握手响应
            print("📥 等待握手响应...")
            response = self.socket.recv(1024).decode()
            print(f"📥 收到握手响应 ({len(response)} 字节):")
            print(response)
            
            if "101" in response and "Upgrade" in response:
                print("✅ WebSocket握手成功!")
                self.connected = True
                return True
            else:
                print("❌ WebSocket握手失败!")
                return False
                
        except Exception as e:
            print(f"❌ 连接失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def create_rfc6455_frame(self, data, opcode=0x1):
        """创建标准RFC6455 WebSocket帧"""
        if isinstance(data, str):
            data = data.encode('utf-8')
        
        print(f"🔧 创建RFC6455帧:")
        print(f"   操作码: {opcode} ({'文本' if opcode == 0x1 else '二进制' if opcode == 0x2 else '其他'})")
        print(f"   数据长度: {len(data)} 字节")
        print(f"   数据内容: {data}")
        
        # RFC6455帧格式：
        # FIN(1) + RSV(3) + OPCODE(4) + MASK(1) + PAYLOAD_LEN(7/16/64) + MASKING_KEY(32) + PAYLOAD
        
        # 第一个字节：FIN=1, RSV=000, OPCODE=opcode
        first_byte = 0x80 | opcode  # FIN=1, OPCODE=text(1) or binary(2)
        print(f"   第一字节: 0x{first_byte:02x} (FIN=1, RSV=000, OPCODE={opcode})")
        
        # 计算负载长度
        payload_len = len(data)
        
        if payload_len < 126:
            # 短负载：7位长度
            second_byte = 0x80 | payload_len  # MASK=1 + 长度
            frame_header = struct.pack('!BB', first_byte, second_byte)
            print(f"   短负载格式: 长度={payload_len}")
        elif payload_len < 65536:
            # 中等负载：16位长度
            second_byte = 0x80 | 126  # MASK=1 + 126
            frame_header = struct.pack('!BBH', first_byte, second_byte, payload_len)
            print(f"   中等负载格式: 长度={payload_len}")
        else:
            # 长负载：64位长度
            second_byte = 0x80 | 127  # MASK=1 + 127
            frame_header = struct.pack('!BBQ', first_byte, second_byte, payload_len)
            print(f"   长负载格式: 长度={payload_len}")
        
        print(f"   第二字节: 0x{second_byte:02x} (MASK=1, LEN={payload_len if payload_len < 126 else 126 if payload_len < 65536 else 127})")
        
        # 生成4字节掩码
        masking_key = os.urandom(4)
        print(f"   掩码密钥: {masking_key.hex()}")
        
        # 对负载进行掩码处理
        masked_payload = bytearray()
        for i, byte in enumerate(data):
            masked_payload.append(byte ^ masking_key[i % 4])
        
        print(f"   掩码处理: 原始{len(data)}字节 -> 掩码{len(masked_payload)}字节")
        
        # 组装完整帧
        frame = frame_header + masking_key + bytes(masked_payload)
        
        print(f"   完整帧: {len(frame)} 字节")
        print(f"   帧头: {frame_header.hex()}")
        print(f"   掩码: {masking_key.hex()}")
        print(f"   负载: {bytes(masked_payload).hex()}")
        print(f"   完整帧(十六进制): {frame.hex()}")
        
        return frame
    
    def send_frame_with_debug(self, data, opcode=0x1):
        """发送帧并进行详细调试"""
        if not self.connected:
            print("❌ 未连接到服务器")
            return False
            
        try:
            print(f"\n🚀 开始发送WebSocket帧...")
            
            frame = self.create_rfc6455_frame(data, opcode)
            
            print(f"\n📤 发送帧到服务器...")
            print(f"   目标: {self.host}:{self.port}")
            print(f"   帧大小: {len(frame)} 字节")
            
            # 分块发送以确保数据完整性
            total_sent = 0
            chunk_size = 1024
            
            while total_sent < len(frame):
                chunk = frame[total_sent:total_sent + chunk_size]
                sent_bytes = self.socket.send(chunk)
                total_sent += sent_bytes
                
                print(f"   发送块: {sent_bytes} 字节 (总计: {total_sent}/{len(frame)})")
                
                if sent_bytes == 0:
                    print("❌ 发送失败: 连接已断开")
                    return False
                
                # 小延迟确保数据传输
                time.sleep(0.01)
            
            print(f"✅ 帧发送完成: {total_sent} 字节")
            
            # 确保数据发送到网络
            print("🔄 刷新socket缓冲区...")
            try:
                self.socket.settimeout(0.1)
                # 尝试读取任何立即可用的响应
                immediate_response = self.socket.recv(1024)
                if immediate_response:
                    print(f"📥 立即收到响应: {len(immediate_response)} 字节")
                    print(f"   内容: {immediate_response}")
            except socket.timeout:
                print("⏰ 没有立即响应")
            except Exception as e:
                print(f"⚠️  检查立即响应时出错: {e}")
            
            return True
            
        except Exception as e:
            print(f"❌ 发送失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def wait_for_response(self, timeout=30):
        """等待服务器响应"""
        if not self.connected:
            print("❌ 未连接到服务器")
            return None
            
        try:
            print(f"\n📥 等待服务器响应 (超时: {timeout}秒)...")
            
            # 设置超时
            self.socket.settimeout(timeout)
            
            start_time = time.time()
            
            while time.time() - start_time < timeout:
                try:
                    # 接收数据
                    data = self.socket.recv(4096)
                    
                    if data:
                        elapsed = time.time() - start_time
                        print(f"📥 收到响应 (耗时: {elapsed:.2f}秒):")
                        print(f"   数据长度: {len(data)} 字节")
                        print(f"   原始数据: {data}")
                        print(f"   十六进制: {data.hex()}")
                        
                        # 尝试解析RFC6455帧
                        if len(data) >= 2:
                            first_byte = data[0]
                            second_byte = data[1]
                            
                            fin = (first_byte & 0x80) >> 7
                            opcode = first_byte & 0x0F
                            mask = (second_byte & 0x80) >> 7
                            payload_len = second_byte & 0x7F
                            
                            print(f"   RFC6455帧解析:")
                            print(f"     FIN: {fin}")
                            print(f"     OPCODE: {opcode} ({'文本' if opcode == 0x1 else '二进制' if opcode == 0x2 else '其他'})")
                            print(f"     MASK: {mask}")
                            print(f"     LEN: {payload_len}")
                            
                            if opcode == 0x1 and payload_len < 126 and not mask:  # 文本帧
                                try:
                                    payload = data[2:2+payload_len]
                                    text = payload.decode('utf-8')
                                    print(f"   文本内容: {text}")
                                    return text
                                except Exception as e:
                                    print(f"   文本解析失败: {e}")
                        
                        # 尝试直接解析为文本
                        try:
                            text = data.decode('utf-8')
                            print(f"   直接文本解析: {text}")
                            return text
                        except:
                            print(f"   无法解析为文本")
                            return data
                    else:
                        print("📥 收到空数据，连接可能已关闭")
                        return None
                        
                except socket.timeout:
                    # 继续等待
                    elapsed = time.time() - start_time
                    remaining = timeout - elapsed
                    if remaining > 0:
                        print(f"⏰ 继续等待... (剩余: {remaining:.1f}秒)")
                        time.sleep(0.5)
                    else:
                        break
                except Exception as e:
                    print(f"❌ 接收数据时出错: {e}")
                    return None
            
            print(f"⏰ 等待超时 ({timeout}秒)")
            return None
                
        except Exception as e:
            print(f"❌ 等待响应失败: {e}")
            import traceback
            traceback.print_exc()
            return None
    
    def close(self):
        """关闭连接"""
        if self.socket:
            print("🔌 关闭WebSocket连接")
            self.socket.close()
            self.connected = False

def test_debug_websocket():
    """调试版WebSocket测试"""
    print("=" * 80)
    print("调试版WebSocket客户端测试")
    print("=" * 80)
    
    # 创建客户端
    client = DebugWebSocketClient('127.0.0.1', 9200)
    
    try:
        # 连接服务器
        print("\n🔗 步骤1: 建立连接")
        if not client.connect():
            print("❌ 连接失败")
            return False
        
        # 等待连接稳定
        print("\n⏰ 步骤2: 等待连接稳定...")
        time.sleep(2)
        
        # 发送用户绑定消息
        print("\n📨 步骤3: 发送用户绑定消息")
        bind_message = {
            "route": "/user/bind",
            "userId": "test_user_123",
            "token": "simple_test_token_456"
        }
        
        message_str = json.dumps(bind_message)
        print(f"消息内容: {message_str}")
        
        if client.send_frame_with_debug(message_str, opcode=0x1):
            print("✅ 消息发送成功")
            
            # 等待响应
            print("\n📥 步骤4: 等待服务器响应")
            response = client.wait_for_response(timeout=30)
            
            if response:
                print(f"🎉 收到服务器响应!")
                
                try:
                    response_data = json.loads(response)
                    print("✅ JSON解析成功:")
                    print(json.dumps(response_data, indent=2, ensure_ascii=False))
                    
                    if response_data.get("ec") == 0:
                        print("🎉 用户绑定成功!")
                        return True
                    else:
                        print(f"⚠️  绑定失败: {response_data.get('info', '未知错误')}")
                        return True  # 至少收到了响应
                        
                except json.JSONDecodeError:
                    print(f"响应不是JSON格式，但收到了响应: {response}")
                    return True  # 至少收到了响应
                    
            else:
                print("❌ 没有收到服务器响应")
                return False
        else:
            print("❌ 消息发送失败")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        client.close()

def check_test_token():
    """检查测试token是否存在"""
    print("=" * 80)
    print("检查测试token")
    print("=" * 80)
    
    try:
        import redis
        r = redis.Redis(host='127.0.0.1', port=6379, db=1)
        
        token = "simple_test_token_456"
        user_id = r.get(token)
        
        if user_id:
            print(f"✅ Token验证成功: {token} -> {user_id.decode()}")
            return True
        else:
            print(f"❌ Token不存在: {token}")
            return False
            
    except Exception as e:
        print(f"❌ Redis检查失败: {e}")
        return False

def main():
    """主函数"""
    # 先检查token
    if not check_test_token():
        print("\n请先运行: python setup_simple_test_token.py")
        return
    
    # 然后测试连接
    success = test_debug_websocket()
    
    print("\n" + "=" * 80)
    print("调试版WebSocket测试结果")
    print("=" * 80)
    
    if success:
        print("🎉 测试成功!")
        print("✅ WebSocket连接和消息传输正常")
        print("✅ 服务器能够接收和处理消息")
    else:
        print("❌ 测试失败!")
        print("❌ 需要进一步分析问题")
        
        print("\n建议:")
        print("1. 检查服务器日志中的详细信息")
        print("2. 确认WebSocket帧格式是否正确")
        print("3. 分析网络传输是否有问题")
    
    print("=" * 80)

if __name__ == '__main__':
    print("开始调试版WebSocket测试...")
    main()
