
import sys
import os
import traceback
import time

# 设置日志
import logging
logging.basicConfig(level=logging.DEBUG, 
                   format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                   handlers=[
                       logging.FileHandler(r"I:\live_room_service-master\live_room_service-master\deploy\logs\CO000001.log", encoding='utf-8'),
                       logging.StreamHandler()
                   ])
logger = logging.getLogger('CO000001')

try:
    # 添加模拟的stackless模块到路径
    logger.debug("添加stackless模拟路径: %s", r"I:\live_room_service-master\live_room_service-master\deploy\bin\stackless_simple")
    sys.path.insert(0, r"I:\live_room_service-master\live_room_service-master\deploy\bin\stackless_simple")
    
    logger.debug("添加bin路径: %s", r"I:\live_room_service-master\live_room_service-master\deploy\bin")
    sys.path.insert(1, r"I:\live_room_service-master\live_room_service-master\deploy\bin")
    
    # 导入必要的模块
    logger.debug("开始导入server模块")
    from tomato.server import server
    logger.debug("成功导入server模块")
    
    # 检查Redis连接
    try:
        import socket
        s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        s.settimeout(1)
        s.connect(('127.0.0.1', 6379))
        s.close()
        logger.debug("Redis连接成功")
    except Exception as e:
        logger.error("Redis连接失败: %s", str(e))
        input("Redis连接失败，按回车键退出...")
        sys.exit(1)
    
    # 运行服务
    logger.debug("开始启动服务: CO000001")
    logger.debug("配置文件路径: %s", r"I:\live_room_service-master\live_room_service-master\deploy\config")
    
    # 打印更多调试信息
    import os
    logger.debug("当前工作目录: %s", os.getcwd())
    logger.debug("配置文件是否存在: %s", os.path.exists(r"I:\live_room_service-master\live_room_service-master\deploy\config"))
    
    # 打印服务配置信息
    try:
        import json
        with open(os.path.join(r"I:\live_room_service-master\live_room_service-master\deploy\config", "CO000001.json"), 'r', encoding='utf-8') as f:
            config = json.load(f)
            logger.debug("服务配置: %s", json.dumps(config, ensure_ascii=False, indent=2))
    except Exception as e:
        logger.error("读取配置文件失败: %s", str(e))
    
    # 启动服务并捕获所有异常
    try:
        server.runWithFileConf("CO000001", r"I:\live_room_service-master\live_room_service-master\deploy\config", __import__("fqparty").app, [])
    except Exception as e:
        logger.error("服务运行时出错: %s", str(e))
        logger.error(traceback.format_exc())
except Exception as e:
    logger.error("启动过程中出错: %s", str(e))
    logger.error(traceback.format_exc())

input("服务已停止，按回车键退出...")
