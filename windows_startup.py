#!/usr/bin/env python
# -*- coding=utf-8 -*-
'''
Windows环境下启动所有服务的脚本
适用于Python 3.8
'''

import os
import sys
import subprocess
import time
import socket
import threading

# 项目路径配置
current_dir = os.path.dirname(os.path.abspath(__file__))
bin_dir = os.path.join(current_dir, 'deploy', 'bin')
config_path = os.path.join(current_dir, 'deploy', 'config')
logs_path = os.path.join(current_dir, 'deploy', 'logs')
run_py = os.path.join(bin_dir, 'tomato', 'run.py')

# 确保日志目录存在
os.makedirs(logs_path, exist_ok=True)

def check_port_in_use(port):
    """检查端口是否被占用"""
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.settimeout(1)
            result = s.connect_ex(('127.0.0.1', port))
            return result == 0
    except:
        return False

def check_redis():
    """检查Redis是否运行"""
    try:
        import redis
        r = redis.Redis(host='127.0.0.1', port=6379, db=0, socket_timeout=2)
        r.ping()
        return True
    except:
        return False

def install_dependencies():
    """安装Python依赖"""
    print("正在检查并安装Python依赖...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("依赖安装完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"依赖安装失败: {e}")
        return False

def start_redis_windows():
    """在Windows上启动Redis"""
    print("正在尝试启动Redis...")
    try:
        # 尝试使用Windows Redis服务
        subprocess.run(["net", "start", "redis"], check=False, capture_output=True)
        time.sleep(2)
        if check_redis():
            print("Redis服务启动成功")
            return True
    except:
        pass
    
    print("无法启动Redis服务，请手动安装并启动Redis")
    print("下载地址: https://github.com/microsoftarchive/redis/releases")
    return False

def create_stackless_mock():
    """创建stackless模块的模拟实现"""
    stackless_path = os.path.join(current_dir, 'stackless.py')
    if not os.path.exists(stackless_path):
        # 复制已有的stackless.py文件
        source_stackless = os.path.join(bin_dir, 'stackless.py')
        if os.path.exists(source_stackless):
            import shutil
            shutil.copy2(source_stackless, stackless_path)
            print("已创建stackless模块模拟")
        else:
            print("警告：未找到stackless.py文件")

def start_service(service_id):
    """启动指定的服务"""
    # 检查关键端口
    if service_id == 'HT000001' and check_port_in_use(9000):
        print(f"警告：HTTP服务端口9000已被占用！")
        return None
    
    if service_id == 'CO000001' and check_port_in_use(9200):
        print(f"警告：WebSocket服务端口9200已被占用！")
        return None
    
    # 设置环境变量
    env = dict(os.environ)
    env["PYTHONPATH"] = f"{current_dir};{bin_dir}"
    
    cmd = [
        sys.executable,
        run_py,
        service_id,
        'fqparty',
        config_path,
        logs_path,
        '0'  # reload参数
    ]
    
    print(f"启动服务 {service_id}...")
    
    # 在新的控制台窗口中启动服务
    try:
        process = subprocess.Popen(
            cmd,
            env=env,
            creationflags=subprocess.CREATE_NEW_CONSOLE,
            cwd=current_dir
        )
        return process
    except Exception as e:
        print(f"启动服务 {service_id} 失败: {e}")
        return None

def main():
    print("=" * 60)
    print("直播间服务启动脚本 - Windows版本")
    print("Python版本:", sys.version)
    print("=" * 60)
    
    # 1. 安装依赖
    if not install_dependencies():
        print("依赖安装失败，请手动安装requirements.txt中的依赖")
        input("按回车键继续...")
    
    # 2. 创建stackless模拟
    create_stackless_mock()
    
    # 3. 检查Redis
    if not check_redis():
        print("Redis未运行，正在尝试启动...")
        if not start_redis_windows():
            print("请确保Redis已安装并运行在127.0.0.1:6379")
            choice = input("是否继续启动服务？(y/n): ")
            if choice.lower() != 'y':
                return
    else:
        print("Redis连接正常")
    
    # 4. 启动所有服务
    services = [
        'CO000001',  # 连接服务器
        'US000001',  # 用户服务器  
        'HT000001',  # HTTP服务器
        'RM000001'   # 房间服务器
    ]
    
    processes = []
    
    for service_id in services:
        process = start_service(service_id)
        if process:
            processes.append((service_id, process))
            time.sleep(1)  # 给服务启动时间
    
    if processes:
        print("\n" + "=" * 60)
        print("所有服务启动完成！")
        print("每个服务都在独立的控制台窗口中运行")
        print("=" * 60)
        
        # 显示服务信息
        for service_id, _ in processes:
            if service_id == 'HT000001':
                print(f"HTTP服务: http://127.0.0.1:9000/")
            elif service_id == 'CO000001':
                print(f"WebSocket服务: ws://127.0.0.1:9200/")
        
        print("\n日志文件位置:", logs_path)
        print("关闭相应的控制台窗口可以停止对应的服务")
        print("=" * 60)
    else:
        print("没有服务成功启动")
    
    input("\n按回车键退出启动脚本...")

if __name__ == '__main__':
    main()
