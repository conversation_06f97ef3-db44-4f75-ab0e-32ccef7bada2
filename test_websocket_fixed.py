#!/usr/bin/env python
# -*- coding=utf-8 -*-
'''
修复版WebSocket测试 - 确保消息能被服务器接收
'''

import asyncio
import websockets
import json
import time

async def test_websocket_fixed():
    """修复版WebSocket测试"""
    uri = "ws://127.0.0.1:9200/"
    
    print("=" * 60)
    print("修复版WebSocket测试")
    print("=" * 60)
    print(f"连接地址: {uri}")
    
    try:
        # 使用更长的超时时间，确保连接稳定
        async with websockets.connect(uri, timeout=15, ping_interval=None, ping_timeout=None) as websocket:
            print("\n✅ WebSocket连接成功!")
            print(f"连接状态: {websocket.state}")
            
            # 等待连接完全建立
            await asyncio.sleep(1)
            
            # 测试1: 发送最简单的JSON消息
            print(f"\n--- 测试1: 简单JSON消息 ---")
            test_message = {
                "route": "/user/bind",
                "userId": "test_user_123",
                "token": "simple_test_token_456"
            }
            
            message_str = json.dumps(test_message)
            print(f"发送: {message_str}")
            
            # 确保消息被发送
            await websocket.send(message_str)
            await websocket.ping()  # 发送ping确保连接活跃
            print("✅ 消息发送成功")
            
            # 等待响应
            print("等待服务器响应...")
            try:
                response = await asyncio.wait_for(websocket.recv(), timeout=15.0)
                print(f"🎉 收到响应: {response}")
                
                try:
                    response_data = json.loads(response)
                    print("✅ 解析后的响应:")
                    print(json.dumps(response_data, indent=2, ensure_ascii=False))
                    
                    if response_data.get("ec") == 0:
                        print("🎉 用户绑定成功!")
                        return True
                    else:
                        print(f"⚠️  绑定失败: {response_data.get('info', '未知错误')}")
                        
                except json.JSONDecodeError:
                    print(f"响应不是JSON格式: {response}")
                    return True  # 至少收到了响应
                    
            except asyncio.TimeoutError:
                print("⚠️  响应超时")
            
            await asyncio.sleep(2)
            
            # 测试2: 发送进入房间消息
            print(f"\n--- 测试2: 进入房间消息 ---")
            room_message = {
                "route": "/user/bindAndEnterRoom",
                "roomId": "test_room_001",
                "authToken": "simple_test_token_456",
                "platform": "test_platform",
                "version": "1.0.0"
            }
            
            message_str = json.dumps(room_message)
            print(f"发送: {message_str}")
            
            await websocket.send(message_str)
            await websocket.ping()  # 发送ping确保连接活跃
            print("✅ 消息发送成功")
            
            # 等待响应
            print("等待服务器响应...")
            try:
                response = await asyncio.wait_for(websocket.recv(), timeout=15.0)
                print(f"🎉 收到响应: {response}")
                
                try:
                    response_data = json.loads(response)
                    print("✅ 解析后的响应:")
                    print(json.dumps(response_data, indent=2, ensure_ascii=False))
                    return True
                except json.JSONDecodeError:
                    print(f"响应不是JSON格式: {response}")
                    return True  # 至少收到了响应
                    
            except asyncio.TimeoutError:
                print("⚠️  响应超时")
            
            # 测试3: 发送心跳消息
            print(f"\n--- 测试3: 心跳消息 ---")
            heartbeat_message = {
                "route": "/heartbeat",
                "timestamp": int(time.time())
            }
            
            message_str = json.dumps(heartbeat_message)
            print(f"发送: {message_str}")
            
            await websocket.send(message_str)
            await websocket.ping()  # 发送ping确保连接活跃
            print("✅ 消息发送成功")
            
            # 等待响应
            print("等待服务器响应...")
            try:
                response = await asyncio.wait_for(websocket.recv(), timeout=10.0)
                print(f"🎉 收到响应: {response}")
                
                try:
                    response_data = json.loads(response)
                    print("✅ 解析后的响应:")
                    print(json.dumps(response_data, indent=2, ensure_ascii=False))
                    return True
                except json.JSONDecodeError:
                    print(f"响应不是JSON格式: {response}")
                    return True  # 至少收到了响应
                    
            except asyncio.TimeoutError:
                print("⚠️  心跳响应超时")
            
            # 保持连接，监听推送消息
            print(f"\n--- 监听服务器推送 ---")
            print("保持连接20秒，监听服务器推送消息...")
            
            try:
                for i in range(20):
                    try:
                        message = await asyncio.wait_for(websocket.recv(), timeout=1.0)
                        print(f"\n📨 收到服务器推送: {message}")
                        
                        try:
                            msg_data = json.loads(message)
                            print("解析后的推送消息:")
                            print(json.dumps(msg_data, indent=2, ensure_ascii=False))
                            return True
                        except json.JSONDecodeError:
                            print(f"推送消息不是JSON格式: {message}")
                            return True
                            
                    except asyncio.TimeoutError:
                        print(".", end="", flush=True)
                        
                        # 每5秒发送一次ping保持连接
                        if i % 5 == 0:
                            try:
                                await websocket.ping()
                            except:
                                pass
                    
                    await asyncio.sleep(1)
                    
            except websockets.exceptions.ConnectionClosed:
                print("\n⚠️  连接被服务器关闭")
            
            print(f"\n\n❌ 没有收到任何服务器响应")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_test_token():
    """检查测试token是否存在"""
    print("=" * 60)
    print("检查测试token")
    print("=" * 60)
    
    try:
        import redis
        r = redis.Redis(host='127.0.0.1', port=6379, db=1)
        
        token = "simple_test_token_456"
        user_id = r.get(token)
        
        if user_id:
            print(f"✅ Token验证成功: {token} -> {user_id.decode()}")
            return True
        else:
            print(f"❌ Token不存在: {token}")
            return False
            
    except Exception as e:
        print(f"❌ Redis检查失败: {e}")
        return False

async def main():
    """主函数"""
    # 先检查token
    if not check_test_token():
        print("\n请先运行: python setup_simple_test_token.py")
        return
    
    # 然后测试连接
    success = await test_websocket_fixed()
    
    print("\n" + "=" * 60)
    print("修复版测试结果")
    print("=" * 60)
    
    if success:
        print("🎉 WebSocket服务器响应成功!")
        print("✅ 服务器工作正常，能够接收和处理WebSocket消息")
    else:
        print("❌ WebSocket服务器响应失败!")
        print("❌ 可能存在消息接收或处理问题")
        
        print("\n建议检查:")
        print("1. 服务器日志中是否有dataReceived消息")
        print("2. 服务器日志中是否有parseFrames调用")
        print("3. 服务器日志中是否有消息处理错误")
    
    print("=" * 60)

if __name__ == '__main__':
    try:
        import websockets
        print("开始修复版WebSocket测试...")
        asyncio.run(main())
    except ImportError:
        print("websockets库未安装")
        print("pip install websockets")
