#!/usr/bin/env python
# -*- coding=utf-8 -*-
'''
修补Redis客户端，使用同步连接替代异步连接
'''

import os
import sys

def patch_redis_client():
    """修补Redis客户端"""
    current_dir = os.path.dirname(os.path.abspath(__file__))
    bin_dir = os.path.join(current_dir, 'deploy', 'bin')
    client_file = os.path.join(bin_dir, 'tomato', 'db', 'redis', 'client.py')
    
    print("修补Redis客户端...")
    
    # 读取原文件
    with open(client_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 创建修补版本
    patched_content = '''# -*- coding=utf-8 -*-
"""
修补版Redis客户端 - 使用同步连接
"""

import redis
from tomato.utils import ttlog

class MockRedisConnection:
    """模拟Redis连接对象"""
    def __init__(self, redis_client):
        self.client = redis_client
        self.maxDelay = 3
        
    def send(self, *args):
        """发送命令"""
        try:
            if len(args) == 1:
                return self.client.execute_command(args[0])
            else:
                return self.client.execute_command(*args)
        except Exception as e:
            ttlog.error('Redis command failed:', args, 'error:', e)
            raise

def connectRedis(host, port, db, password=None, timeout=30, maxDelay=3):
    """连接Redis - 同步版本"""
    ttlog.info('connectRedis',
               'host=', host,
               'port=', port,
               'db=', db,
               'password=', password)
    
    try:
        # 使用标准redis库
        client = redis.Redis(
            host=host,
            port=port,
            db=db,
            password=password or None,
            socket_timeout=timeout,
            socket_connect_timeout=timeout
        )
        
        # 测试连接
        client.ping()
        
        # 返回模拟的连接对象
        conn = MockRedisConnection(client)
        return conn
        
    except Exception as e:
        ttlog.error('connectRedis failed:', e)
        raise

def connectRedisCluster(confList, timeout, maxDelay):
    """连接Redis集群 - 同步版本"""
    connList = []
    try:
        for conf in confList:
            conn = connectRedis(conf['host'], conf['port'], conf['db'], conf.get('password'), timeout, maxDelay)
            connList.append(conn)
        return connList
    except:
        for conn in connList:
            closeRedis(conn)
        raise

def closeRedis(conn):
    """关闭Redis连接"""
    try:
        if hasattr(conn, 'client'):
            conn.client.close()
    except:
        pass

# 保持其他类和函数的兼容性
class TTRedisClientFactory:
    def __init__(self, *args, **kwargs):
        pass

class TTRedisPool:
    def __init__(self):
        self._redis = {}
        self._subscribers = {}
    
    def getRedis(self, host, port, db, timeout):
        key = (host, port, db)
        r = self._redis.get(key)
        if not r:
            r = connectRedis(host, port, db, timeout=timeout)
            self._redis[key] = r
        return r
'''
    
    # 备份原文件
    backup_file = client_file + '.backup'
    if not os.path.exists(backup_file):
        with open(backup_file, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"   ✅ 已备份原文件到: {backup_file}")
    
    # 写入修补版本
    with open(client_file, 'w', encoding='utf-8') as f:
        f.write(patched_content)
    
    print(f"   ✅ 已修补Redis客户端: {client_file}")

def main():
    print("=" * 60)
    print("Redis客户端修补工具")
    print("=" * 60)
    
    patch_redis_client()
    
    print("\n修补完成！现在可以尝试启动服务了。")
    print("运行命令: python debug_start_service.py HT000001")

if __name__ == '__main__':
    main()
