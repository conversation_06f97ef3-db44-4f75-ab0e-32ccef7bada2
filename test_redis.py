#!/usr/bin/env python
# -*- coding=utf-8 -*-
'''
测试Redis连接的脚本
'''

import redis

def test_redis_connection():
    print("测试Redis连接...")
    
    # 测试不同的连接配置
    configs = [
        {"host": "127.0.0.1", "port": 6379, "db": 0, "password": None},
        {"host": "127.0.0.1", "port": 6379, "db": 0, "password": ""},
        {"host": "127.0.0.1", "port": 6379, "db": 0, "password": ""},
    ]
    
    for i, config in enumerate(configs, 1):
        print(f"\n{i}. 测试配置: {config}")
        try:
            r = redis.Redis(**config, socket_timeout=2)
            r.ping()
            print("   ✅ 连接成功!")
            
            # 测试基本操作
            r.set("test_key", "test_value")
            value = r.get("test_key")
            print(f"   ✅ 读写测试成功: {value}")
            r.delete("test_key")
            
            return config
            
        except redis.AuthenticationError as e:
            print(f"   ❌ 认证失败: {e}")
        except redis.ConnectionError as e:
            print(f"   ❌ 连接失败: {e}")
        except Exception as e:
            print(f"   ❌ 其他错误: {e}")
    
    return None

if __name__ == '__main__':
    working_config = test_redis_connection()
    
    if working_config:
        print(f"\n🎉 找到可用的Redis配置: {working_config}")
        print("\n建议更新配置文件中的Redis密码设置")
    else:
        print("\n❌ 所有Redis配置都失败了")
        print("请检查:")
        print("1. Redis服务是否运行")
        print("2. Redis密码配置")
        print("3. 防火墙设置")
