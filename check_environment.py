#!/usr/bin/env python
# -*- coding=utf-8 -*-
'''
检查运行环境的脚本
'''

import os
import sys
import socket
import subprocess

def check_python_version():
    """检查Python版本"""
    print(f"Python版本: {sys.version}")
    if sys.version_info < (3, 6):
        print("❌ Python版本过低，建议使用Python 3.6+")
        return False
    else:
        print("✅ Python版本符合要求")
        return True

def check_redis():
    """检查Redis连接"""
    try:
        import redis
        r = redis.Redis(host='127.0.0.1', port=6379, db=0, socket_timeout=2)
        r.ping()
        print("✅ Redis连接正常")
        return True
    except ImportError:
        print("❌ Redis模块未安装，请运行: pip install redis")
        return False
    except Exception as e:
        print(f"❌ Redis连接失败: {e}")
        print("请确保Redis服务运行在127.0.0.1:6379")
        return False

def check_ports():
    """检查关键端口"""
    ports = {
        9000: "HTTP服务",
        9200: "WebSocket服务"
    }
    
    all_free = True
    for port, service in ports.items():
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.settimeout(1)
                result = s.connect_ex(('127.0.0.1', port))
                if result == 0:
                    print(f"⚠️  端口{port}({service})已被占用")
                    all_free = False
                else:
                    print(f"✅ 端口{port}({service})可用")
        except Exception as e:
            print(f"❌ 检查端口{port}时出错: {e}")
            all_free = False
    
    return all_free

def check_dependencies():
    """检查Python依赖"""
    required_modules = [
        'twisted',
        'eventlet', 
        'redis',
        'pymysql',
        'psutil',
        'msgpack'
    ]
    
    missing = []
    for module in required_modules:
        try:
            __import__(module)
            print(f"✅ {module}")
        except ImportError:
            print(f"❌ {module} 未安装")
            missing.append(module)
    
    if missing:
        print(f"\n缺少依赖: {', '.join(missing)}")
        print("请运行: pip install -r requirements.txt")
        return False
    else:
        print("✅ 所有依赖已安装")
        return True

def check_project_structure():
    """检查项目结构"""
    current_dir = os.path.dirname(os.path.abspath(__file__))
    required_paths = [
        'deploy/bin',
        'deploy/config',
        'deploy/bin/tomato/run.py',
        'deploy/bin/fqparty',
        'deploy/config/server'
    ]
    
    all_exist = True
    for path in required_paths:
        full_path = os.path.join(current_dir, path)
        if os.path.exists(full_path):
            print(f"✅ {path}")
        else:
            print(f"❌ {path} 不存在")
            all_exist = False
    
    return all_exist

def main():
    print("=" * 60)
    print("直播间服务环境检查")
    print("=" * 60)
    
    checks = [
        ("Python版本", check_python_version),
        ("项目结构", check_project_structure),
        ("Python依赖", check_dependencies),
        ("Redis服务", check_redis),
        ("端口可用性", check_ports)
    ]
    
    results = []
    for name, check_func in checks:
        print(f"\n检查 {name}:")
        print("-" * 30)
        result = check_func()
        results.append((name, result))
    
    print("\n" + "=" * 60)
    print("检查结果汇总:")
    print("=" * 60)
    
    all_passed = True
    for name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{name}: {status}")
        if not result:
            all_passed = False
    
    print("\n" + "=" * 60)
    if all_passed:
        print("🎉 所有检查通过！可以启动服务了")
        print("运行命令: python windows_startup.py")
    else:
        print("⚠️  存在问题，请先解决上述问题再启动服务")
    print("=" * 60)

if __name__ == '__main__':
    main()
