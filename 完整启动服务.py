#!/usr/bin/env python
# -*- coding=utf-8 -*-
'''
使用更完整的stackless模拟实现启动所有服务
'''

import os
import sys
import subprocess
import time
import threading

# 配置路径
current_dir = os.path.dirname(os.path.abspath(__file__))
deploy_path = os.path.join(current_dir, 'deploy')
bin_dir = os.path.join(deploy_path, 'bin')
config_path = os.path.join(deploy_path, 'config')
logs_path = os.path.join(deploy_path, 'logs')

# 确保日志目录存在
os.makedirs(logs_path, exist_ok=True)

# 创建更完整的stackless模拟模块
stackless_dir = os.path.join(bin_dir, 'stackless_complete')
os.makedirs(stackless_dir, exist_ok=True)

with open(os.path.join(stackless_dir, '__init__.py'), 'w') as f:
    f.write('''
# 完整的stackless模拟模块
import threading
import queue
import time
import sys
import traceback

# 全局任务队列
_task_queue = queue.Queue()
_current_tasklet = None
_main_tasklet = None
_channel_registry = {}
_tasklet_registry = {}

class TaskletExit(Exception):
    pass

class bomb(Exception):
    """当tasklet被杀死时引发的异常"""
    def __init__(self, exc_type=None, exc_value=None, exc_traceback=None):
        self.exc_type = exc_type
        self.exc_value = exc_value
        self.exc_traceback = exc_traceback

class channel:
    """模拟stackless中的通道"""
    def __init__(self):
        self.queue = queue.Queue()
        self.balance = 0
        self.preference = 0
        self.closing = False
    
    def send(self, value):
        """向通道发送值"""
        if self.closing:
            raise ValueError("Cannot send on a closed channel")
        self.balance += 1
        self.queue.put(value)
        schedule()
        return value
    
    def receive(self):
        """从通道接收值"""
        if self.closing and self.queue.empty():
            raise StopIteration("No more values in channel")
        self.balance -= 1
        value = self.queue.get()
        schedule()
        return value
    
    def close(self):
        """关闭通道"""
        self.closing = True

class tasklet:
    """模拟stackless中的tasklet"""
    def __init__(self, func=None, *args, **kwargs):
        global _tasklet_registry
        self.func = func
        self.args = args
        self.kwargs = kwargs
        self.alive = False
        self.blocked = False
        self.paused = False
        self._task_id = id(self)
        self.next = None
        _tasklet_registry[self._task_id] = self
    
    def __call__(self, *args, **kwargs):
        """使tasklet可调用"""
        if args or kwargs:
            self.args = args
            self.kwargs = kwargs
        return self
    
    def setup(self, *args, **kwargs):
        """设置tasklet的参数"""
        if args or kwargs:
            self.args = args
            self.kwargs = kwargs
        return self
    
    def insert(self):
        """将tasklet插入调度队列"""
        global _task_queue
        if not self.alive:
            self.alive = True
            _task_queue.put(self)
        return self
    
    def remove(self):
        """从调度队列中移除tasklet"""
        self.alive = False
        return self
    
    def kill(self):
        """杀死tasklet"""
        self.alive = False
        return self
    
    def run(self):
        """运行tasklet的函数"""
        global _current_tasklet
        _current_tasklet = self
        try:
            if self.func:
                result = self.func(*self.args, **self.kwargs)
                return result
        except Exception as e:
            traceback.print_exc()
        finally:
            self.alive = False
            _current_tasklet = None

def getcurrent():
    """获取当前运行的tasklet"""
    global _current_tasklet
    if _current_tasklet is None:
        # 如果没有当前tasklet，返回主tasklet
        global _main_tasklet
        if _main_tasklet is None:
            _main_tasklet = tasklet()
            _main_tasklet.alive = True
        return _main_tasklet
    return _current_tasklet

def schedule():
    """执行一次调度循环"""
    global _task_queue, _current_tasklet
    
    # 如果当前tasklet还活着，把它放回队列
    if _current_tasklet and _current_tasklet.alive and not _current_tasklet.blocked:
        _task_queue.put(_current_tasklet)
    
    # 尝试获取下一个tasklet
    try:
        next_task = _task_queue.get(block=False)
        if next_task and next_task.alive and not next_task.blocked:
            _current_tasklet = next_task
            next_task.run()
    except queue.Empty:
        pass

def run():
    """运行调度器，直到没有更多的tasklet"""
    while not _task_queue.empty():
        schedule()

def runcount(count):
    """运行调度器指定的次数"""
    for _ in range(count):
        if _task_queue.empty():
            break
        schedule()

# 创建主tasklet
_main_tasklet = tasklet()
_main_tasklet.alive = True
_current_tasklet = _main_tasklet
''')

# 检查Redis是否运行
def check_redis():
    try:
        import socket
        s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        s.settimeout(1)
        s.connect(('127.0.0.1', 6379))
        s.close()
        return True
    except:
        return False

# 检查端口是否被占用
def check_port_in_use(port):
    import socket
    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
        return s.connect_ex(('127.0.0.1', port)) == 0

# 创建启动脚本
def create_startup_script(service_id):
    startup_script = os.path.join(current_dir, f"start_{service_id}.py")
    with open(startup_script, 'w') as f:
        f.write(f'''
import sys
import os
import traceback

# 设置日志
import logging
logging.basicConfig(level=logging.DEBUG, 
                   format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('{service_id}')

try:
    # 添加模拟的stackless模块到路径
    logger.debug("添加stackless模拟路径: %s", r"{stackless_dir}")
    sys.path.insert(0, r"{stackless_dir}")
    
    logger.debug("添加bin路径: %s", r"{bin_dir}")
    sys.path.insert(1, r"{bin_dir}")
    
    # 导入必要的模块
    logger.debug("开始导入server模块")
    from tomato.server import server
    logger.debug("成功导入server模块")
    
    # 检查Redis连接
    try:
        import socket
        s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        s.settimeout(1)
        s.connect(('127.0.0.1', 6379))
        s.close()
        logger.debug("Redis连接成功")
    except Exception as e:
        logger.error("Redis连接失败: %s", str(e))
        input("Redis连接失败，按回车键退出...")
        sys.exit(1)
    
    # 运行服务
    logger.debug("开始启动服务: {service_id}")
    server.runWithFileConf("{service_id}", r"{config_path}", __import__("fqparty").app, [])
except Exception as e:
    logger.error("启动过程中出错: %s", str(e))
    logger.error(traceback.format_exc())
    input("发生错误，按回车键退出...")
''')
    return startup_script

# 启动服务
def start_service(service_id):
    # 检查端口占用
    if service_id == 'HT000001' and check_port_in_use(9000):
        print(f"警告：HTTP服务端口9000已被占用！请关闭占用该端口的程序后重试。")
        return None
    
    if service_id == 'CO000001' and check_port_in_use(9200):
        print(f"警告：连接服务器端口9200已被占用！请关闭占用该端口的程序后重试。")
        return None
    
    # 创建启动脚本
    startup_script = create_startup_script(service_id)
    
    # 使用当前Python解释器
    python_path = sys.executable
    
    env = dict(os.environ)
    env["PYTHONPATH"] = f"{stackless_dir};{bin_dir}"
    
    print(f"启动服务 {service_id}...")
    
    # 在新窗口中启动服务
    process = subprocess.Popen(
        [python_path, startup_script],
        env=env,
        creationflags=subprocess.CREATE_NEW_CONSOLE
    )
    
    return process

# 安装Redis
def install_redis():
    print("您需要安装Redis服务才能运行此项目。")
    print("1. 下载Windows版Redis: https://github.com/microsoftarchive/redis/releases")
    print("2. 安装并启动Redis服务")
    print("3. 确保Redis运行在默认端口6379上")
    
    choice = input("是否现在下载Redis安装程序？(y/n): ")
    if choice.lower() == 'y':
        import webbrowser
        webbrowser.open("https://github.com/microsoftarchive/redis/releases/download/win-3.2.100/Redis-x64-3.2.100.msi")
        print("下载已开始，请安装Redis并启动服务后再运行此脚本。")
        return False
    return False

if __name__ == "__main__":
    print("开始启动所有服务...")
    
    # 检查Redis
    if not check_redis():
        print("警告：无法连接到Redis服务！")
        if not install_redis():
            print("请安装并启动Redis服务后再运行此脚本。")
            sys.exit(1)
    else:
        print("Redis连接正常")
    
    # 服务ID列表
    services = [
        'CO000001',  # 连接服务器
        'US000001',  # 用户服务器
        'HT000001',  # HTTP服务器
        'RM000001'   # 房间服务器
    ]
    
    processes = []
    
    for service_id in services:
        process = start_service(service_id)
        if process:
            processes.append((service_id, process))
    
    if processes:
        print("\n所有服务已启动！")
        print("每个服务都在独立的控制台窗口中运行")
        print("关闭相应的控制台窗口可以停止服务")
        
        if any(sid == 'HT000001' for sid, _ in processes):
            print("\nHTTP服务运行在: http://127.0.0.1:9000/")
        
        if any(sid == 'CO000001' for sid, _ in processes):
            print("WebSocket服务运行在: ws://127.0.0.1:9200/")
    
    print("\n按回车键退出此脚本...")
    input() 