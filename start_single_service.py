#!/usr/bin/env python
# -*- coding=utf-8 -*-
'''
启动单个服务的脚本，用于调试
'''

import os
import sys
import subprocess

def main():
    if len(sys.argv) < 2:
        print("用法: python start_single_service.py <服务ID>")
        print("可用的服务ID:")
        print("  CO000001 - 连接服务器")
        print("  US000001 - 用户服务器")
        print("  HT000001 - HTTP服务器")
        print("  RM000001 - 房间服务器")
        return
    
    service_id = sys.argv[1]
    
    # 项目路径配置
    current_dir = os.path.dirname(os.path.abspath(__file__))
    bin_dir = os.path.join(current_dir, 'deploy', 'bin')
    config_path = os.path.join(current_dir, 'deploy', 'config')
    logs_path = os.path.join(current_dir, 'deploy', 'logs')
    run_py = os.path.join(bin_dir, 'tomato', 'run.py')
    
    # 确保日志目录存在
    os.makedirs(logs_path, exist_ok=True)
    
    # 设置环境变量
    env = dict(os.environ)
    env["PYTHONPATH"] = f"{current_dir};{bin_dir}"
    
    cmd = [
        sys.executable,
        run_py,
        service_id,
        'fqparty',
        config_path,
        logs_path,
        '0'
    ]
    
    print(f"启动服务 {service_id}...")
    print(f"命令: {' '.join(cmd)}")
    print(f"工作目录: {current_dir}")
    print(f"日志目录: {logs_path}")
    print("-" * 50)
    
    # 直接在当前控制台运行，便于查看输出
    try:
        print("按 Ctrl+C 停止服务")
        print("-" * 50)
        subprocess.run(cmd, env=env, cwd=current_dir)
    except KeyboardInterrupt:
        print(f"\n服务 {service_id} 已停止")
    except Exception as e:
        print(f"启动服务失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
