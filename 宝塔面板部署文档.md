# live_room_service 宝塔面板部署文档

## 📋 **部署概述**

live_room_service 是一个基于Python的直播间服务系统，包含WebSocket连接服务、用户服务、房间服务和HTTP服务。本文档将指导您在宝塔面板上完整部署此服务。

## 🛠 **系统要求**

- **操作系统**: Linux (Ubuntu 18.04+ / CentOS 7+)
- **Python版本**: Python 3.8+
- **内存**: 最低 2GB，推荐 4GB+
- **磁盘空间**: 最低 10GB
- **宝塔面板**: 7.0+

## 📦 **依赖服务**

- **Redis**: 数据缓存和消息队列
- **Python环境**: 运行应用服务
- **Supervisor**: 进程管理 (推荐)

## 🚀 **部署步骤**

### 步骤1: 安装宝塔面板依赖

#### 1.1 安装Redis
```bash
# 在宝塔面板 -> 软件商店 -> 运行环境 中安装Redis
# 或者通过命令行安装
yum install redis -y  # CentOS
apt install redis-server -y  # Ubuntu

# 启动Redis服务
systemctl start redis
systemctl enable redis
```

#### 1.2 安装Python 3.8+
```bash
# 在宝塔面板 -> 软件商店 -> 运行环境 中安装Python项目管理器
# 或者通过命令行安装
yum install python3 python3-pip -y  # CentOS
apt install python3 python3-pip -y  # Ubuntu
```

#### 1.3 安装Supervisor (推荐)
```bash
# 在宝塔面板 -> 软件商店 -> 系统工具 中安装Supervisor
# 或者通过命令行安装
pip3 install supervisor
```

### 步骤2: 上传项目文件

#### 2.1 创建项目目录
```bash
# 在宝塔面板 -> 文件 中创建目录
mkdir -p /www/wwwroot/live_room_service
cd /www/wwwroot/live_room_service
```

#### 2.2 上传项目文件
将整个 `live_room_service-master` 目录上传到 `/www/wwwroot/live_room_service/`

项目结构应该如下：
```
/www/wwwroot/live_room_service/
├── deploy/
├── fqparty-conf/
├── fqparty-py/
├── tomato-py/
├── requirements.txt
├── start_all_services_final.py
├── start_websocket_service.py
└── README.md
```

### 步骤3: 安装Python依赖

#### 3.1 创建虚拟环境 (推荐)
```bash
cd /www/wwwroot/live_room_service
python3 -m venv venv
source venv/bin/activate
```

#### 3.2 安装依赖包
```bash
# 激活虚拟环境后安装
pip install -r requirements.txt

# 如果requirements.txt不完整，手动安装核心依赖
pip install twisted redis stackless
```

### 步骤4: 配置Redis

#### 4.1 修改Redis配置
```bash
# 编辑Redis配置文件
vim /etc/redis/redis.conf

# 确保以下配置正确
bind 127.0.0.1
port 6379
daemonize yes
```

#### 4.2 重启Redis服务
```bash
systemctl restart redis
systemctl status redis
```

### 步骤5: 配置服务启动脚本

#### 5.1 创建启动脚本
```bash
# 创建启动脚本
vim /www/wwwroot/live_room_service/start_service.sh
```

启动脚本内容：
```bash
#!/bin/bash

# 设置项目路径
PROJECT_PATH="/www/wwwroot/live_room_service"
cd $PROJECT_PATH

# 激活虚拟环境
source venv/bin/activate

# 设置Python路径
export PYTHONPATH=$PROJECT_PATH/deploy/bin:$PROJECT_PATH/fqparty-py/src:$PROJECT_PATH/tomato-py/src:$PYTHONPATH

# 启动服务
python start_all_services_final.py
```

#### 5.2 设置脚本权限
```bash
chmod +x /www/wwwroot/live_room_service/start_service.sh
```

### 步骤6: 配置Supervisor进程管理

#### 6.1 创建Supervisor配置文件
```bash
vim /etc/supervisor/conf.d/live_room_service.conf
```

配置文件内容：
```ini
[program:live_room_service]
command=/www/wwwroot/live_room_service/start_service.sh
directory=/www/wwwroot/live_room_service
user=root
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/www/wwwroot/live_room_service/logs/service.log
stdout_logfile_maxbytes=50MB
stdout_logfile_backups=10
environment=PYTHONPATH="/www/wwwroot/live_room_service/deploy/bin:/www/wwwroot/live_room_service/fqparty-py/src:/www/wwwroot/live_room_service/tomato-py/src"
```

#### 6.2 创建日志目录
```bash
mkdir -p /www/wwwroot/live_room_service/logs
```

#### 6.3 重新加载Supervisor配置
```bash
supervisorctl reread
supervisorctl update
supervisorctl start live_room_service
```

### 步骤7: 配置防火墙和端口

#### 7.1 开放必要端口
在宝塔面板 -> 安全 -> 防火墙 中添加以下端口：

- **9200**: WebSocket连接服务
- **9000**: HTTP服务 (可选)
- **6379**: Redis服务 (仅内网访问)

#### 7.2 系统防火墙配置
```bash
# CentOS/RHEL
firewall-cmd --permanent --add-port=9200/tcp
firewall-cmd --permanent --add-port=9000/tcp
firewall-cmd --reload

# Ubuntu
ufw allow 9200/tcp
ufw allow 9000/tcp
```

## 🔧 **服务管理**

### 启动服务
```bash
supervisorctl start live_room_service
```

### 停止服务
```bash
supervisorctl stop live_room_service
```

### 重启服务
```bash
supervisorctl restart live_room_service
```

### 查看服务状态
```bash
supervisorctl status live_room_service
```

### 查看服务日志
```bash
tail -f /www/wwwroot/live_room_service/logs/service.log
```

## 📊 **服务验证**

### 检查端口状态
```bash
netstat -tlnp | grep :9200
netstat -tlnp | grep :6379
```

### 检查Redis连接
```bash
redis-cli ping
```

### 测试WebSocket连接
```bash
# 使用curl测试WebSocket握手
curl -i -N -H "Connection: Upgrade" -H "Upgrade: websocket" -H "Sec-WebSocket-Key: test" -H "Sec-WebSocket-Version: 13" http://your-server-ip:9200/
```

## 🌐 **域名和SSL配置 (可选)**

### 配置反向代理
在宝塔面板 -> 网站 中创建站点，配置反向代理：

```nginx
# WebSocket代理配置
location /ws/ {
    proxy_pass http://127.0.0.1:9200/;
    proxy_http_version 1.1;
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection "upgrade";
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
}

# HTTP API代理配置
location /api/ {
    proxy_pass http://127.0.0.1:9000/;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
}
```

## 🔍 **故障排除**

### 常见问题

#### 1. 服务启动失败
```bash
# 检查Python路径
which python3
# 检查依赖安装
pip list | grep twisted
# 检查权限
ls -la /www/wwwroot/live_room_service/
```

#### 2. Redis连接失败
```bash
# 检查Redis状态
systemctl status redis
# 检查Redis配置
redis-cli ping
```

#### 3. 端口被占用
```bash
# 查看端口占用
netstat -tlnp | grep :9200
# 杀死占用进程
kill -9 <PID>
```

#### 4. 权限问题
```bash
# 设置正确的文件权限
chown -R root:root /www/wwwroot/live_room_service/
chmod -R 755 /www/wwwroot/live_room_service/
```

## 📝 **维护建议**

1. **定期备份**: 备份项目文件和Redis数据
2. **日志轮转**: 配置日志文件自动清理
3. **监控告警**: 设置服务状态监控
4. **安全更新**: 定期更新系统和依赖包

## 🎯 **部署完成**

部署完成后，您的服务将在以下地址可用：

- **WebSocket服务**: `ws://your-server-ip:9200/`
- **HTTP服务**: `http://your-server-ip:9000/` (如果启用)

现在您可以使用客户端应用连接到WebSocket服务，开始使用直播间功能！
