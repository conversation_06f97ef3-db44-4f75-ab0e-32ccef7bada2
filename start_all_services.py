#!/usr/bin/env python
# -*- coding=utf-8 -*-
'''
一次性启动所有直播间服务的脚本
'''

import os
import sys
import subprocess
import time

def main():
    print("=" * 60)
    print("启动所有直播间服务")
    print("=" * 60)
    
    # 项目路径配置
    current_dir = os.path.dirname(os.path.abspath(__file__))
    bin_dir = os.path.join(current_dir, 'deploy', 'bin')
    config_path = os.path.join(current_dir, 'deploy', 'config')
    logs_path = os.path.join(current_dir, 'deploy', 'logs')
    run_py = os.path.join(bin_dir, 'tomato', 'run.py')
    
    # 确保日志目录存在
    os.makedirs(logs_path, exist_ok=True)
    
    # 服务列表
    services = [
        ('CO000001', '连接服务器'),
        ('US000001', '用户服务器'),
        ('HT000001', 'HTTP服务器'),
        ('RM000001', '房间服务器')
    ]
    
    # 设置环境变量
    env = dict(os.environ)
    env["PYTHONPATH"] = f"{current_dir};{bin_dir}"
    
    processes = []
    
    print("开始启动服务...")
    print("-" * 40)
    
    for service_id, service_name in services:
        print(f"启动 {service_name} ({service_id})...")
        
        cmd = [
            sys.executable,
            run_py,
            service_id,
            'fqparty',
            config_path,
            logs_path,
            '0'
        ]
        
        try:
            # 在新的控制台窗口中启动服务
            process = subprocess.Popen(
                cmd,
                env=env,
                creationflags=subprocess.CREATE_NEW_CONSOLE,
                cwd=current_dir
            )
            processes.append((service_id, service_name, process))
            print(f"  ✅ {service_name} 启动成功")
            time.sleep(1)  # 给每个服务一点启动时间
            
        except Exception as e:
            print(f"  ❌ {service_name} 启动失败: {e}")
    
    if processes:
        print("\n" + "=" * 60)
        print("所有服务启动完成！")
        print("=" * 60)
        
        print("\n运行中的服务:")
        for service_id, service_name, process in processes:
            if service_id == 'HT000001':
                print(f"  🌐 {service_name}: http://127.0.0.1:9000/")
            elif service_id == 'CO000001':
                print(f"  🔌 {service_name}: ws://127.0.0.1:9200/")
            else:
                print(f"  ⚙️  {service_name}: 运行中")
        
        print(f"\n📁 日志目录: {logs_path}")
        print("💡 每个服务都在独立的控制台窗口中运行")
        print("💡 关闭对应的控制台窗口可以停止相应的服务")
        
        print("\n" + "=" * 60)
        print("服务启动完成！您现在可以:")
        print("1. 访问 http://127.0.0.1:9000/ 测试HTTP服务")
        print("2. 连接 ws://127.0.0.1:9200/ 测试WebSocket服务")
        print("3. 查看日志文件了解服务运行状态")
        print("=" * 60)
    else:
        print("\n❌ 没有服务成功启动")
    
    input("\n按回车键退出...")

if __name__ == '__main__':
    main()
