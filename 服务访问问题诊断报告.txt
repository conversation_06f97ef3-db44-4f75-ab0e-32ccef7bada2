===== 服务访问问题诊断报告 =====

== 系统信息 ==
操作系统: Windows 10.0.26100
Python版本: 3.8.2 (tags/v3.8.2:7b3ab59, Feb 25 2020, 23:03:10) [MSC v.1916 64 bit (AMD64)]
管理员权限: 否

== 端口检查 ==
HTTP端口(9000)未被占用
WebSocket端口(9200)未被占用

== Redis检查 ==
Redis连接正常

== 防火墙检查 ==
无法获取防火墙状态

== HTTP服务测试 ==
测试HTTP服务器响应异常

== 网络接口检查 ==
主机名: Anthony
本地回环地址: 127.0.0.1
IP地址列表:
  - ************
  - *************
  - ************


== hosts文件检查 ==
hosts文件中没有找到localhost条目

== 可能的问题和解决方案 ==
4. 无法启动或访问测试HTTP服务
   解决方案: 检查网络配置，尝试以管理员身份运行
5. 脚本未以管理员权限运行
   解决方案: 以管理员身份运行脚本
