#!/usr/bin/env python
# -*- coding=utf-8 -*-
'''
简单的消息发送测试
'''

import asyncio
import websockets
import json

async def test_simple_message():
    """测试简单消息发送"""
    uri = "ws://127.0.0.1:9200/"
    
    print("=" * 60)
    print("简单消息发送测试")
    print("=" * 60)
    print(f"连接地址: {uri}")
    
    try:
        async with websockets.connect(uri, timeout=10) as websocket:
            print("✅ WebSocket连接成功!")
            
            # 发送一个最简单的消息
            simple_message = {
                "msgType": 1,
                "msgId": 1001,
                "route": "/user/bind",
                "body": {
                    "userId": "100216",
                    "token": "072d576456d38bfc41208c404fc3c087"
                }
            }
            
            message_str = json.dumps(simple_message)
            print(f"\n发送消息: {message_str}")
            
            await websocket.send(message_str)
            print("✅ 消息发送成功")
            
            # 等待响应
            print("等待响应...")
            try:
                response = await asyncio.wait_for(websocket.recv(), timeout=15.0)
                print(f"✅ 收到响应: {response}")
                
                try:
                    response_data = json.loads(response)
                    print("解析后的响应:")
                    print(json.dumps(response_data, indent=2, ensure_ascii=False))
                except json.JSONDecodeError:
                    print(f"响应不是JSON格式: {response}")
                    
            except asyncio.TimeoutError:
                print("⚠️  响应超时")
                
            # 保持连接一段时间，看看是否有其他消息
            print("\n保持连接5秒，监听其他消息...")
            for i in range(5):
                try:
                    message = await asyncio.wait_for(websocket.recv(), timeout=1.0)
                    print(f"收到额外消息: {message}")
                except asyncio.TimeoutError:
                    print(".", end="", flush=True)
                await asyncio.sleep(1)
            
            print(f"\n测试完成")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == '__main__':
    asyncio.run(test_simple_message())
