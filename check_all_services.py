#!/usr/bin/env python
# -*- coding=utf-8 -*-
'''
检查所有live_room_service服务状态
'''

import socket
import redis
import requests
import json
import time

def check_port(host, port, service_name):
    """检查端口是否开放"""
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(3)
        result = sock.connect_ex((host, port))
        sock.close()
        
        if result == 0:
            print(f"✅ {service_name} (端口 {port}): 正在运行")
            return True
        else:
            print(f"❌ {service_name} (端口 {port}): 未运行")
            return False
    except Exception as e:
        print(f"❌ {service_name} (端口 {port}): 检查失败 - {e}")
        return False

def check_redis():
    """检查Redis服务"""
    try:
        r = redis.Redis(host='127.0.0.1', port=6379, db=0)
        ping_result = r.ping()
        
        if ping_result:
            print("✅ Redis服务: 正在运行")
            
            # 检查数据库连接
            try:
                # 检查db=0 (默认数据库)
                r0 = redis.Redis(host='127.0.0.1', port=6379, db=0)
                r0.ping()
                print("  ✅ Redis DB 0: 连接正常")
                
                # 检查db=1 (token数据库)
                r1 = redis.Redis(host='127.0.0.1', port=6379, db=1)
                r1.ping()
                print("  ✅ Redis DB 1: 连接正常")
                
                # 检查db=9 (其他数据库)
                r9 = redis.Redis(host='127.0.0.1', port=6379, db=9)
                r9.ping()
                print("  ✅ Redis DB 9: 连接正常")
                
                # 检查测试token
                token = "simple_test_token_456"
                user_id = r1.get(token)
                if user_id:
                    print(f"  ✅ 测试Token: {token} -> {user_id.decode()}")
                else:
                    print(f"  ⚠️  测试Token不存在: {token}")
                
                return True
                
            except Exception as e:
                print(f"  ❌ Redis数据库连接失败: {e}")
                return False
                
        else:
            print("❌ Redis服务: 未运行")
            return False
            
    except Exception as e:
        print(f"❌ Redis服务: 检查失败 - {e}")
        return False

def check_websocket():
    """检查WebSocket服务"""
    try:
        import websockets
        import asyncio
        
        async def test_websocket():
            try:
                uri = "ws://127.0.0.1:9200/"
                websocket = await websockets.connect(uri)
                await websocket.close()
                return True
            except Exception as e:
                print(f"  ❌ WebSocket连接失败: {e}")
                return False
        
        result = asyncio.run(test_websocket())
        if result:
            print("✅ WebSocket服务: 正在运行并可连接")
            return True
        else:
            print("❌ WebSocket服务: 连接失败")
            return False
            
    except ImportError:
        # 如果没有websockets库，使用socket检查
        if check_port('127.0.0.1', 9200, 'WebSocket服务'):
            print("  ⚠️  端口开放，但未测试WebSocket连接 (需要安装websockets库)")
            return True
        return False
    except Exception as e:
        print(f"❌ WebSocket服务: 检查失败 - {e}")
        return False

def check_http_service():
    """检查HTTP服务"""
    try:
        response = requests.get('http://127.0.0.1:9000/', timeout=5)
        print(f"✅ HTTP服务 (端口 9000): 正在运行 (状态码: {response.status_code})")
        return True
    except requests.exceptions.ConnectionError:
        print("❌ HTTP服务 (端口 9000): 未运行")
        return False
    except requests.exceptions.Timeout:
        print("⚠️  HTTP服务 (端口 9000): 响应超时")
        return True  # 端口开放但响应慢
    except Exception as e:
        print(f"❌ HTTP服务 (端口 9000): 检查失败 - {e}")
        return False

def check_service_configuration():
    """检查服务配置"""
    print("\n📋 服务配置检查:")
    
    # 检查配置文件
    config_files = [
        "deploy/config/servers.json",
        "deploy/config/redis.json",
        "deploy/config/log.json"
    ]
    
    for config_file in config_files:
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            print(f"  ✅ 配置文件: {config_file}")
        except FileNotFoundError:
            print(f"  ❌ 配置文件不存在: {config_file}")
        except json.JSONDecodeError:
            print(f"  ❌ 配置文件格式错误: {config_file}")
        except Exception as e:
            print(f"  ❌ 配置文件检查失败: {config_file} - {e}")

def main():
    """主函数"""
    print("=" * 80)
    print("live_room_service 服务状态检查")
    print("=" * 80)
    
    services_status = {}
    
    print("\n🔍 基础服务检查:")
    
    # 检查Redis
    services_status['redis'] = check_redis()
    
    print("\n🔍 应用服务检查:")
    
    # 检查WebSocket连接服务 (CO000001)
    services_status['websocket'] = check_websocket()
    
    # 检查HTTP服务 (HT000001)
    services_status['http'] = check_http_service()
    
    # 检查其他端口
    print("\n🔍 其他服务端口检查:")
    
    # 用户服务 (US000001) - 通常不直接监听端口
    print("  ℹ️  用户服务 (US000001): 后端服务，不直接监听端口")
    
    # 房间服务 (RM000001) - 通常不直接监听端口  
    print("  ℹ️  房间服务 (RM000001): 后端服务，不直接监听端口")
    
    # 检查配置
    check_service_configuration()
    
    # 总结
    print("\n" + "=" * 80)
    print("服务状态总结")
    print("=" * 80)
    
    total_services = len(services_status)
    running_services = sum(services_status.values())
    
    print(f"📊 服务状态: {running_services}/{total_services} 个服务正在运行")
    
    if running_services == total_services:
        print("🎉 所有服务都正常运行!")
        print("\n✅ 您的live_room_service已经完全启动并可以使用")
        print("\n📋 服务地址:")
        print("  - WebSocket连接: ws://127.0.0.1:9200/")
        print("  - HTTP服务: http://127.0.0.1:9000/")
        print("  - Redis数据库: 127.0.0.1:6379")
        
        print("\n🚀 下一步:")
        print("  1. 使用您的客户端应用连接到 ws://127.0.0.1:9200/")
        print("  2. 发送用户绑定和进入房间的消息")
        print("  3. 开始使用直播间服务")
        
    else:
        print("⚠️  部分服务未运行，请检查:")
        for service, status in services_status.items():
            if not status:
                print(f"  ❌ {service}")
        
        print("\n💡 建议:")
        print("  1. 检查服务启动脚本是否正确执行")
        print("  2. 查看服务日志中的错误信息")
        print("  3. 确认所有依赖服务都已启动")
    
    print("=" * 80)

if __name__ == '__main__':
    main()
