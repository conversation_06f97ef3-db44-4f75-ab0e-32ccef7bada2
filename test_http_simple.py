#!/usr/bin/env python
# -*- coding=utf-8 -*-
'''
简单的HTTP服务测试
'''

import os
import sys
import time

def main():
    print("=" * 60)
    print("简单HTTP服务测试")
    print("=" * 60)
    
    # 项目路径配置
    current_dir = os.path.dirname(os.path.abspath(__file__))
    bin_dir = os.path.join(current_dir, 'deploy', 'bin')
    config_path = os.path.join(current_dir, 'deploy', 'config')
    logs_path = os.path.join(current_dir, 'deploy', 'logs')
    
    # 设置路径
    sys.path.insert(0, bin_dir)
    
    try:
        print("1. 导入模块...")
        import stackless
        import tomato
        from tomato.config import configure
        from tomato.utils import ttlog
        import fqparty
        print("   [OK] 模块导入成功")
        
        print("2. 初始化配置...")
        configure.initByFile(config_path)
        print("   [OK] 配置初始化成功")
        
        print("3. 设置日志级别...")
        logLevel = ttlog.INFO
        ttlog.setLevel(logLevel)
        print(f"   [OK] 日志级别: {logLevel}")
        
        print("4. 初始化tomato应用...")
        tomato.app.appArgs = []
        tomato.app.init('HT000001')
        print("   [OK] tomato应用初始化成功")
        
        print("5. 初始化fqparty应用...")
        fqparty.app.init()
        print("   [OK] fqparty应用初始化成功")
        
        print("6. 启动fqparty应用...")
        fqparty.app.start()
        print("   [OK] fqparty应用启动成功")
        
        print("7. 启动tomato应用...")
        tomato.app.start()
        print("   [OK] tomato应用启动成功")
        
        print("\n" + "=" * 60)
        print("HTTP服务器启动成功!")
        print("=" * 60)
        print("访问地址: http://127.0.0.1:9000/")
        print("测试API: http://127.0.0.1:9000/iapi/reloadConfig")
        print("按 Ctrl+C 停止服务")
        print("=" * 60)
        
        # 检查端口是否在监听
        import socket
        def check_port():
            try:
                with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                    s.settimeout(1)
                    result = s.connect_ex(('127.0.0.1', 9000))
                    return result == 0
            except:
                return False
        
        # 等待端口开始监听
        for i in range(10):
            if check_port():
                print(f"\n[OK] 端口9000正在监听")
                break
            else:
                print(f"等待端口9000开始监听... ({i+1}/10)")
                time.sleep(1)
        else:
            print("\n[WARNING] 端口9000未开始监听")
        
        # 保持服务运行
        print("\n服务运行中...")
        try:
            while True:
                time.sleep(5)
                if not check_port():
                    print("[WARNING] 端口9000不再监听")
                    break
                print(".", end="", flush=True)
        except KeyboardInterrupt:
            print("\n\n服务已停止")
        
    except Exception as e:
        print(f"\n[ERROR] 启动失败: {e}")
        import traceback
        traceback.print_exc()
        input("\n按回车键退出...")

if __name__ == '__main__':
    main()
