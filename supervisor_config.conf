# live_room_service Supervisor 配置文件
# 将此文件复制到 /etc/supervisor/conf.d/ 目录下
# 文件名: live_room_service.conf

[program:live_room_service]
# 启动命令 (请根据实际部署路径修改)
command=/www/wwwroot/live_room_service/start_bt.sh

# 工作目录
directory=/www/wwwroot/live_room_service

# 运行用户 (建议使用非root用户，如www)
user=root

# 自动启动
autostart=true

# 自动重启
autorestart=true

# 启动重试次数
startretries=3

# 重启间隔 (秒)
autorestart_pause=10

# 停止信号
stopsignal=TERM

# 停止等待时间 (秒)
stopwaitsecs=10

# 重定向stderr到stdout
redirect_stderr=true

# 日志文件
stdout_logfile=/www/wwwroot/live_room_service/logs/supervisor.log

# 日志文件最大大小
stdout_logfile_maxbytes=50MB

# 日志文件备份数量
stdout_logfile_backups=10

# 环境变量
environment=PYTHONPATH="/www/wwwroot/live_room_service/deploy/bin:/www/wwwroot/live_room_service/fqparty-py/src:/www/wwwroot/live_room_service/tomato-py/src"

# 进程优先级
priority=999

# 启动延迟 (秒)
startsecs=5

# 进程组
process_name=%(program_name)s
