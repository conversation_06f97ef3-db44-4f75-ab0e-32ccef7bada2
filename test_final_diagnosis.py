#!/usr/bin/env python
# -*- coding=utf-8 -*-
'''
最终诊断测试 - 确认服务器问题
'''

import asyncio
import websockets
import json

async def test_final_diagnosis():
    """最终诊断测试"""
    uri = "ws://127.0.0.1:9200/"
    
    print("=" * 60)
    print("最终诊断测试")
    print("=" * 60)
    print(f"连接地址: {uri}")
    print("目标：确认服务器是否能接收WebSocket消息")
    
    try:
        async with websockets.connect(uri, timeout=10) as websocket:
            print("\n✅ WebSocket连接成功!")
            print(f"连接状态: {websocket.state}")
            
            # 发送最简单的消息
            print(f"\n--- 发送最简单的测试消息 ---")
            test_message = "Hello Server"
            
            print(f"发送文本消息: {test_message}")
            await websocket.send(test_message)
            print("✅ 文本消息发送成功")
            
            # 等待响应
            print("等待服务器响应...")
            try:
                response = await asyncio.wait_for(websocket.recv(), timeout=10.0)
                print(f"🎉 收到响应: {response}")
                return True
            except asyncio.TimeoutError:
                print("⚠️  文本消息响应超时")
            
            # 发送JSON消息
            print(f"\n--- 发送JSON消息 ---")
            json_message = {"test": "message"}
            message_str = json.dumps(json_message)
            
            print(f"发送JSON消息: {message_str}")
            await websocket.send(message_str)
            print("✅ JSON消息发送成功")
            
            # 等待响应
            print("等待服务器响应...")
            try:
                response = await asyncio.wait_for(websocket.recv(), timeout=10.0)
                print(f"🎉 收到响应: {response}")
                return True
            except asyncio.TimeoutError:
                print("⚠️  JSON消息响应超时")
            
            # 发送二进制消息
            print(f"\n--- 发送二进制消息 ---")
            binary_message = b"Binary Test Message"
            
            print(f"发送二进制消息: {binary_message}")
            await websocket.send(binary_message)
            print("✅ 二进制消息发送成功")
            
            # 等待响应
            print("等待服务器响应...")
            try:
                response = await asyncio.wait_for(websocket.recv(), timeout=10.0)
                print(f"🎉 收到响应: {response}")
                return True
            except asyncio.TimeoutError:
                print("⚠️  二进制消息响应超时")
            
            # 保持连接，看看是否有任何推送
            print(f"\n--- 监听服务器推送 ---")
            print("保持连接10秒，监听任何服务器消息...")
            
            try:
                for i in range(10):
                    try:
                        message = await asyncio.wait_for(websocket.recv(), timeout=1.0)
                        print(f"\n📨 收到服务器消息: {message}")
                        return True
                    except asyncio.TimeoutError:
                        print(".", end="", flush=True)
                    await asyncio.sleep(1)
                    
            except websockets.exceptions.ConnectionClosed:
                print("\n⚠️  连接被服务器关闭")
            
            print(f"\n\n❌ 没有收到任何服务器响应")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主函数"""
    success = await test_final_diagnosis()
    
    print("\n" + "=" * 60)
    print("最终诊断结果")
    print("=" * 60)
    
    if success:
        print("🎉 服务器响应正常! 服务器工作正常!")
        print("✅ WebSocket消息接收和处理功能正常")
    else:
        print("❌ 服务器响应异常!")
        print("❌ WebSocket消息没有被服务器接收或处理")
        print("\n可能的原因:")
        print("1. WebSocket消息帧解析有问题")
        print("2. Composer配置有问题") 
        print("3. 消息处理器没有正确注册")
        print("4. 服务器内部错误")
        
        print("\n建议:")
        print("1. 检查服务器日志中的错误信息")
        print("2. 确认WebSocket协议版本兼容性")
        print("3. 检查消息格式要求")
    
    print("=" * 60)

if __name__ == '__main__':
    try:
        import websockets
        print("开始最终诊断测试...")
        asyncio.run(main())
    except ImportError:
        print("websockets库未安装")
        print("pip install websockets")
