#!/usr/bin/env python
# -*- coding=utf-8 -*-
'''
使用我自己的数据测试服务器响应 - 确保服务器正常工作
'''

import asyncio
import websockets
import json
import time

async def test_server_response():
    """测试服务器响应 - 使用我自己的数据"""
    uri = "ws://127.0.0.1:9200/"
    
    print("=" * 60)
    print("测试服务器响应 - 使用我自己的数据")
    print("=" * 60)
    print(f"连接地址: {uri}")
    print("测试用户: test_user_123")
    print("测试Token: simple_test_token_456")
    
    try:
        async with websockets.connect(uri, timeout=10) as websocket:
            print("\n✅ WebSocket连接成功!")
            print(f"连接状态: {websocket.state}")
            
            # 测试1: 最简单的用户绑定
            print(f"\n--- 测试1: 用户绑定 ---")
            bind_message = {
                "route": "/user/bind",
                "userId": "test_user_123",
                "token": "simple_test_token_456"
            }
            
            message_str = json.dumps(bind_message)
            print(f"发送: {message_str}")
            
            await websocket.send(message_str)
            print("✅ 消息发送成功")
            
            # 等待响应
            print("等待服务器响应...")
            try:
                response = await asyncio.wait_for(websocket.recv(), timeout=15.0)
                print(f"🎉 收到响应: {response}")
                
                try:
                    response_data = json.loads(response)
                    print("✅ 解析后的响应:")
                    print(json.dumps(response_data, indent=2, ensure_ascii=False))
                    
                    if response_data.get("ec") == 0:
                        print("🎉 用户绑定成功!")
                        return True
                    else:
                        print(f"⚠️  绑定失败: {response_data.get('info', '未知错误')}")
                        
                except json.JSONDecodeError:
                    print(f"响应不是JSON格式: {response}")
                    
            except asyncio.TimeoutError:
                print("⚠️  用户绑定响应超时")
            
            await asyncio.sleep(2)
            
            # 测试2: 进入房间 - 使用简单参数
            print(f"\n--- 测试2: 进入房间 ---")
            room_message = {
                "route": "/user/bindAndEnterRoom",
                "roomId": "test_room_001",
                "authToken": "simple_test_token_456",
                "platform": "test_platform",
                "version": "1.0.0"
            }
            
            message_str = json.dumps(room_message)
            print(f"发送: {message_str}")
            
            await websocket.send(message_str)
            print("✅ 消息发送成功")
            
            # 等待响应
            print("等待服务器响应...")
            try:
                response = await asyncio.wait_for(websocket.recv(), timeout=15.0)
                print(f"🎉 收到响应: {response}")
                
                try:
                    response_data = json.loads(response)
                    print("✅ 解析后的响应:")
                    print(json.dumps(response_data, indent=2, ensure_ascii=False))
                    
                    if response_data.get("ec") == 0:
                        print("🎉 进入房间成功!")
                        return True
                    else:
                        print(f"⚠️  进入房间失败: {response_data.get('info', '未知错误')}")
                        
                except json.JSONDecodeError:
                    print(f"响应不是JSON格式: {response}")
                    
            except asyncio.TimeoutError:
                print("⚠️  进入房间响应超时")
            
            await asyncio.sleep(2)
            
            # 测试3: 尝试更简单的消息格式
            print(f"\n--- 测试3: 简化消息格式 ---")
            simple_message = {
                "route": "/user/bind",
                "userId": "test_user_123",
                "token": "simple_test_token_456"
            }
            
            # 尝试不同的发送方式
            message_str = json.dumps(simple_message, separators=(',', ':'))  # 紧凑格式
            print(f"发送紧凑格式: {message_str}")
            
            await websocket.send(message_str)
            print("✅ 消息发送成功")
            
            # 等待响应
            print("等待服务器响应...")
            try:
                response = await asyncio.wait_for(websocket.recv(), timeout=15.0)
                print(f"🎉 收到响应: {response}")
                
                try:
                    response_data = json.loads(response)
                    print("✅ 解析后的响应:")
                    print(json.dumps(response_data, indent=2, ensure_ascii=False))
                    return True
                except json.JSONDecodeError:
                    print(f"响应不是JSON格式: {response}")
                    
            except asyncio.TimeoutError:
                print("⚠️  简化格式响应超时")
            
            # 测试4: 发送心跳消息
            print(f"\n--- 测试4: 心跳消息 ---")
            heartbeat_message = {
                "route": "/heartbeat",
                "timestamp": int(time.time())
            }
            
            message_str = json.dumps(heartbeat_message)
            print(f"发送: {message_str}")
            
            await websocket.send(message_str)
            print("✅ 消息发送成功")
            
            # 等待响应
            print("等待服务器响应...")
            try:
                response = await asyncio.wait_for(websocket.recv(), timeout=10.0)
                print(f"🎉 收到响应: {response}")
                
                try:
                    response_data = json.loads(response)
                    print("✅ 解析后的响应:")
                    print(json.dumps(response_data, indent=2, ensure_ascii=False))
                    return True
                except json.JSONDecodeError:
                    print(f"响应不是JSON格式: {response}")
                    
            except asyncio.TimeoutError:
                print("⚠️  心跳响应超时")
            
            # 保持连接，监听推送消息
            print(f"\n--- 监听服务器推送 ---")
            print("保持连接15秒，监听服务器推送消息...")
            
            try:
                for i in range(15):
                    try:
                        message = await asyncio.wait_for(websocket.recv(), timeout=1.0)
                        print(f"\n📨 收到服务器推送: {message}")
                        
                        try:
                            msg_data = json.loads(message)
                            print("解析后的推送消息:")
                            print(json.dumps(msg_data, indent=2, ensure_ascii=False))
                            return True
                        except json.JSONDecodeError:
                            print(f"推送消息不是JSON格式: {message}")
                            return True
                            
                    except asyncio.TimeoutError:
                        print(".", end="", flush=True)
                    await asyncio.sleep(1)
                    
            except websockets.exceptions.ConnectionClosed:
                print("\n⚠️  连接被服务器关闭")
            
            print(f"\n\n❌ 没有收到任何服务器响应")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_test_token():
    """检查测试token是否存在"""
    print("=" * 60)
    print("检查测试token")
    print("=" * 60)
    
    try:
        import redis
        r = redis.Redis(host='127.0.0.1', port=6379, db=1)
        
        token = "simple_test_token_456"
        user_id = r.get(token)
        
        if user_id:
            print(f"✅ Token验证成功: {token} -> {user_id.decode()}")
            return True
        else:
            print(f"❌ Token不存在: {token}")
            return False
            
    except Exception as e:
        print(f"❌ Redis检查失败: {e}")
        return False

async def main():
    """主函数"""
    # 先检查token
    if not check_test_token():
        print("\n请先运行: python setup_simple_test_token.py")
        return
    
    # 然后测试连接
    success = await test_server_response()
    
    if success:
        print("\n🎉 服务器响应测试成功! 服务器工作正常!")
    else:
        print("\n❌ 服务器响应测试失败! 可能存在问题!")

if __name__ == '__main__':
    try:
        import websockets
        print("开始测试服务器响应...")
        asyncio.run(main())
    except ImportError:
        print("websockets库未安装")
        print("pip install websockets")
