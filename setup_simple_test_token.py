#!/usr/bin/env python
# -*- coding=utf-8 -*-
'''
设置简单的测试token
'''

import redis

def setup_simple_token():
    """设置简单的测试token"""
    print("=" * 60)
    print("设置简单测试token")
    print("=" * 60)
    
    try:
        # 连接session Redis (db=1)
        session_redis = redis.Redis(host='127.0.0.1', port=6379, db=1)
        session_redis.ping()
        print("✅ 连接到session Redis成功")
        
        # 设置简单的测试数据
        test_user_id = "test_user_123"
        test_token = "simple_test_token_456"
        
        # 在session Redis中设置 token -> userId 的映射
        session_redis.set(test_token, test_user_id)
        print(f"✅ 设置token映射: {test_token} -> {test_user_id}")
        
        # 在session Redis中设置 userId -> token 的映射
        session_redis.set(test_user_id, test_token)
        print(f"✅ 设置用户映射: {test_user_id} -> {test_token}")
        
        # 验证设置
        stored_user_id = session_redis.get(test_token)
        stored_token = session_redis.get(test_user_id)
        
        if stored_user_id and stored_token:
            print(f"✅ 验证成功:")
            print(f"   token '{test_token}' -> userId '{stored_user_id.decode()}'")
            print(f"   userId '{test_user_id}' -> token '{stored_token.decode()}'")
        else:
            print("❌ 验证失败")
            return False
        
        # 设置token过期时间 (1小时)
        session_redis.expire(test_token, 3600)
        session_redis.expire(test_user_id, 3600)
        print("✅ 设置token过期时间: 1小时")
        
        print("\n" + "=" * 60)
        print("简单测试token设置完成!")
        print("=" * 60)
        print(f"用户ID: {test_user_id}")
        print(f"Token: {test_token}")
        print("=" * 60)
        
        return True
        
    except Exception as e:
        print(f"❌ 设置token失败: {e}")
        return False

if __name__ == '__main__':
    setup_simple_token()
