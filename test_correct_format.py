#!/usr/bin/env python
# -*- coding=utf-8 -*-
'''
使用正确格式测试WebSocket连接
'''

import asyncio
import websockets
import json
import struct

async def test_correct_format():
    """使用正确的消息格式测试"""
    uri = "ws://127.0.0.1:9200/"
    
    print("=" * 60)
    print("使用正确格式测试WebSocket连接")
    print("=" * 60)
    print(f"连接地址: {uri}")
    
    try:
        async with websockets.connect(uri, timeout=10) as websocket:
            print("✅ WebSocket连接成功!")
            print(f"连接状态: {websocket.state}")
            
            # 方法1: 尝试旧版本格式 - 直接发送JSON字符串
            print(f"\n--- 方法1: 直接JSON格式 ---")
            bind_message_v1 = {
                "route": "/user/bindAndEnterRoom",
                "roomId": 100216,
                "authToken": "072d576456d38bfc41208c404fc3c087",
                "platform": "iOS,18.3",
                "version": "5.0.0",
                "isReceivePush": 1,
                "onMicForLogout": 1
            }
            
            message_str = json.dumps(bind_message_v1)
            print(f"发送消息: {message_str}")
            
            await websocket.send(message_str)
            print("✅ 消息发送成功")
            
            try:
                response = await asyncio.wait_for(websocket.recv(), timeout=8.0)
                print(f"✅ 收到响应: {response}")
                
                try:
                    response_data = json.loads(response)
                    print("解析后的响应:")
                    print(json.dumps(response_data, indent=2, ensure_ascii=False))
                except json.JSONDecodeError:
                    print(f"响应不是JSON格式: {response}")
                    
            except asyncio.TimeoutError:
                print("⚠️  方法1响应超时")
            
            await asyncio.sleep(2)
            
            # 方法2: 尝试带长度前缀的格式 (TTComposerInt32String)
            print(f"\n--- 方法2: 带长度前缀格式 ---")
            bind_message_v2 = {
                "route": "/user/bindAndEnterRoom",
                "roomId": 100216,
                "authToken": "072d576456d38bfc41208c404fc3c087",
                "platform": "iOS,18.3",
                "version": "5.0.0",
                "isReceivePush": 1,
                "onMicForLogout": 1
            }
            
            message_str = json.dumps(bind_message_v2)
            message_bytes = message_str.encode('utf-8')
            
            # 添加4字节长度前缀 (big-endian)
            length_prefix = struct.pack('!I', len(message_bytes))
            full_message = length_prefix + message_bytes
            
            print(f"发送消息: {message_str}")
            print(f"消息长度: {len(message_bytes)} 字节")
            print(f"完整数据: {full_message.hex()}")
            
            await websocket.send(full_message)
            print("✅ 消息发送成功")
            
            try:
                response = await asyncio.wait_for(websocket.recv(), timeout=8.0)
                print(f"✅ 收到响应: {response}")
                
                try:
                    response_data = json.loads(response)
                    print("解析后的响应:")
                    print(json.dumps(response_data, indent=2, ensure_ascii=False))
                except json.JSONDecodeError:
                    print(f"响应不是JSON格式: {response}")
                    
            except asyncio.TimeoutError:
                print("⚠️  方法2响应超时")
            
            await asyncio.sleep(2)
            
            # 方法3: 尝试标准TTMessage格式
            print(f"\n--- 方法3: TTMessage格式 ---")
            
            # 构造TTMessage
            route = "/user/bindAndEnterRoom"
            body = {
                "roomId": 100216,
                "authToken": "072d576456d38bfc41208c404fc3c087",
                "platform": "iOS,18.3",
                "version": "5.0.0",
                "isReceivePush": 1,
                "onMicForLogout": 1
            }
            
            body_str = json.dumps(body)
            route_bytes = route.encode('utf-8')
            body_bytes = body_str.encode('utf-8')
            
            # TTMessage格式: msgType(1) + msgId(4) + routeLen(4) + bodyLen(4) + route + body
            msg_type = 1  # TYPE_REQUEST
            msg_id = 1001
            route_len = len(route_bytes)
            body_len = len(body_bytes)
            
            # 打包消息头 (big-endian)
            header = struct.pack('!BIBI', msg_type, msg_id, route_len, body_len)
            full_message = header + route_bytes + body_bytes
            
            print(f"发送TTMessage:")
            print(f"  msgType: {msg_type}")
            print(f"  msgId: {msg_id}")
            print(f"  route: {route}")
            print(f"  body: {body_str}")
            print(f"完整数据: {full_message.hex()}")
            
            await websocket.send(full_message)
            print("✅ 消息发送成功")
            
            try:
                response = await asyncio.wait_for(websocket.recv(), timeout=8.0)
                print(f"✅ 收到响应: {response}")
                
                try:
                    response_data = json.loads(response)
                    print("解析后的响应:")
                    print(json.dumps(response_data, indent=2, ensure_ascii=False))
                except json.JSONDecodeError:
                    print(f"响应不是JSON格式: {response}")
                    
            except asyncio.TimeoutError:
                print("⚠️  方法3响应超时")
            
            # 方法4: 尝试简单的用户绑定
            print(f"\n--- 方法4: 简单用户绑定 ---")
            simple_bind = {
                "route": "/user/bind",
                "userId": "100216",
                "token": "072d576456d38bfc41208c404fc3c087"
            }
            
            message_str = json.dumps(simple_bind)
            print(f"发送消息: {message_str}")
            
            await websocket.send(message_str)
            print("✅ 消息发送成功")
            
            try:
                response = await asyncio.wait_for(websocket.recv(), timeout=8.0)
                print(f"✅ 收到响应: {response}")
                
                try:
                    response_data = json.loads(response)
                    print("解析后的响应:")
                    print(json.dumps(response_data, indent=2, ensure_ascii=False))
                except json.JSONDecodeError:
                    print(f"响应不是JSON格式: {response}")
                    
            except asyncio.TimeoutError:
                print("⚠️  方法4响应超时")
            
            # 保持连接，监听推送消息
            print(f"\n--- 监听服务器推送 ---")
            print("保持连接10秒，监听服务器推送...")
            
            try:
                for i in range(10):
                    try:
                        message = await asyncio.wait_for(websocket.recv(), timeout=1.0)
                        print(f"\n📨 收到服务器推送: {message}")
                        
                        try:
                            msg_data = json.loads(message)
                            print("解析后的推送消息:")
                            print(json.dumps(msg_data, indent=2, ensure_ascii=False))
                        except json.JSONDecodeError:
                            print(f"推送消息不是JSON格式: {message}")
                            
                    except asyncio.TimeoutError:
                        print(".", end="", flush=True)
                    await asyncio.sleep(1)
                    
            except websockets.exceptions.ConnectionClosed:
                print("\n⚠️  连接被服务器关闭")
            
            print(f"\n\n✅ 测试完成")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

async def main():
    """主函数"""
    await test_correct_format()

if __name__ == '__main__':
    try:
        import websockets
        print("开始测试正确的消息格式...")
        asyncio.run(main())
    except ImportError:
        print("websockets库未安装")
        print("pip install websockets")
