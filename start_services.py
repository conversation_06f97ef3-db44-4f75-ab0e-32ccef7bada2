#!/usr/bin/env python
# -*- coding=utf-8 -*-
'''
启动直播间服务的最终脚本
'''

import os
import sys
import subprocess
import time
import threading

def main():
    print("=" * 60)
    print("直播间服务启动器")
    print("=" * 60)
    
    services = [
        ('HT000001', 'HTTP服务器', 'http://127.0.0.1:9000/'),
        ('CO000001', '连接服务器', 'ws://127.0.0.1:9200/'),
        ('US000001', '用户服务器', None),
        ('RM000001', '房间服务器', None)
    ]
    
    print("可用的服务:")
    for i, (service_id, service_name, url) in enumerate(services, 1):
        if url:
            print(f"  {i}. {service_name} ({service_id}) - {url}")
        else:
            print(f"  {i}. {service_name} ({service_id})")
    
    print("\n选择启动方式:")
    print("  1. 启动单个服务")
    print("  2. 启动所有服务")
    print("  3. 退出")
    
    choice = input("\n请选择 (1-3): ").strip()
    
    if choice == '1':
        # 启动单个服务
        service_choice = input("请选择要启动的服务 (1-4): ").strip()
        try:
            service_index = int(service_choice) - 1
            if 0 <= service_index < len(services):
                service_id, service_name, url = services[service_index]
                print(f"\n启动 {service_name}...")
                start_single_service(service_id, service_name, url)
            else:
                print("无效的选择")
        except ValueError:
            print("请输入有效的数字")
    
    elif choice == '2':
        # 启动所有服务
        print("\n启动所有服务...")
        start_all_services(services)
    
    elif choice == '3':
        print("退出")
        return
    
    else:
        print("无效的选择")

def start_single_service(service_id, service_name, url):
    """在当前终端启动单个服务"""
    print(f"正在启动 {service_name} ({service_id})...")
    
    # 项目路径配置
    current_dir = os.path.dirname(os.path.abspath(__file__))
    bin_dir = os.path.join(current_dir, 'deploy', 'bin')
    
    # 设置路径
    sys.path.insert(0, bin_dir)
    
    try:
        # 直接在当前进程中启动服务
        import stackless
        import tomato
        from tomato.config import configure
        from tomato.utils import ttlog
        import fqparty
        
        # 初始化配置
        configure.initByFile(os.path.join(current_dir, 'deploy', 'config'))
        
        # 设置日志级别
        logLevel = configure.loadJson('server.tomato.global', {}).get('log', {}).get('level', ttlog.INFO)
        ttlog.setLevel(logLevel)
        
        # 初始化应用
        tomato.app.appArgs = []
        tomato.app.init(service_id)
        fqparty.app.init()
        fqparty.app.start()
        tomato.app.start()
        
        print(f"\n[OK] {service_name} 启动成功!")
        if url:
            print(f"访问地址: {url}")
        print("按 Ctrl+C 停止服务")
        print("-" * 40)
        
        # 保持服务运行
        try:
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            print(f"\n{service_name} 已停止")
        
    except Exception as e:
        print(f"[ERROR] 启动 {service_name} 失败: {e}")
        import traceback
        traceback.print_exc()

def start_all_services(services):
    """启动所有服务"""
    current_dir = os.path.dirname(os.path.abspath(__file__))
    
    processes = []
    
    for service_id, service_name, url in services:
        print(f"\n启动 {service_name} ({service_id})...")
        
        cmd = [
            sys.executable,
            'start_http_service.py' if service_id == 'HT000001' else 'start_single_service.py',
            service_id if service_id != 'HT000001' else ''
        ]
        
        # 过滤空参数
        cmd = [arg for arg in cmd if arg]
        
        try:
            # 在新窗口启动服务
            process = subprocess.Popen(
                cmd,
                cwd=current_dir,
                creationflags=subprocess.CREATE_NEW_CONSOLE
            )
            processes.append((service_id, service_name, process, url))
            print(f"  [OK] {service_name} 启动成功 (PID: {process.pid})")
            time.sleep(2)  # 给服务启动时间
            
        except Exception as e:
            print(f"  [ERROR] {service_name} 启动失败: {e}")
    
    if processes:
        print("\n" + "=" * 60)
        print("所有服务启动完成!")
        print("=" * 60)
        
        print("\n运行中的服务:")
        for service_id, service_name, process, url in processes:
            if url:
                print(f"  [OK] {service_name}: {url}")
            else:
                print(f"  [OK] {service_name}: 运行中")
        
        print("\n每个服务都在独立的控制台窗口中运行")
        print("关闭对应的控制台窗口可以停止相应的服务")
        print("=" * 60)
    
    input("\n按回车键退出...")

if __name__ == '__main__':
    main()
