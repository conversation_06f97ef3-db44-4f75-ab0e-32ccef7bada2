#!/usr/bin/env python
# -*- coding=utf-8 -*-
'''
增强的WebSocket客户端 - 测试修复效果
'''

import socket
import base64
import struct
import json
import time
import os

class EnhancedWebSocketClient:
    '''增强的WebSocket客户端'''

    def __init__(self, host, port):
        self.host = host
        self.port = port
        self.socket = None
        self.connected = False

    def connect(self):
        '''连接到WebSocket服务器'''
        try:
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.connect((self.host, self.port))

            # 发送WebSocket握手
            key = base64.b64encode(os.urandom(16)).decode()
            handshake = (
                f"GET / HTTP/1.1\r\n"
                f"Host: {self.host}:{self.port}\r\n"
                f"Upgrade: websocket\r\n"
                f"Connection: Upgrade\r\n"
                f"Sec-WebSocket-Key: {key}\r\n"
                f"Sec-WebSocket-Version: 13\r\n"
                f"User-Agent: EnhancedWebSocketClient/1.0\r\n"
                f"\r\n"
            )

            print(f"📤 发送握手请求")
            self.socket.send(handshake.encode())

            # 接收握手响应
            response = self.socket.recv(1024).decode()
            print(f"📥 收到握手响应")

            if "101" in response:
                print("✅ WebSocket握手成功!")
                self.connected = True
                return True
            else:
                print("❌ WebSocket握手失败!")
                return False

        except Exception as e:
            print(f"❌ 连接失败: {e}")
            return False

    def send_message(self, message):
        '''发送WebSocket消息'''
        if not self.connected:
            return False

        try:
            # 创建RFC6455帧
            if isinstance(message, str):
                data = message.encode('utf-8')
            else:
                data = message

            # 简单的文本帧格式
            frame = bytearray()
            frame.append(0x81)  # FIN=1, OPCODE=1 (text)

            if len(data) < 126:
                frame.append(0x80 | len(data))  # MASK=1, LEN=data_len
            else:
                frame.append(0x80 | 126)  # MASK=1, LEN=126
                frame.extend(struct.pack('!H', len(data)))

            # 添加掩码
            mask = os.urandom(4)
            frame.extend(mask)

            # 掩码处理数据
            for i, byte in enumerate(data):
                frame.append(byte ^ mask[i % 4])

            print(f"📤 发送消息: {len(frame)} 字节")
            print(f"   内容: {message}")

            # 发送帧
            sent = self.socket.send(bytes(frame))
            print(f"✅ 发送完成: {sent} 字节")

            # 等待一下确保数据发送
            time.sleep(0.1)

            return True

        except Exception as e:
            print(f"❌ 发送失败: {e}")
            return False

    def receive_message(self, timeout=10):
        '''接收WebSocket消息'''
        if not self.connected:
            return None

        try:
            self.socket.settimeout(timeout)
            data = self.socket.recv(4096)

            if data:
                print(f"📥 收到数据: {len(data)} 字节")
                print(f"   十六进制: {data.hex()}")

                try:
                    text = data.decode('utf-8')
                    print(f"   文本内容: {text}")
                    return text
                except:
                    return data
            else:
                return None

        except socket.timeout:
            print("⏰ 接收超时")
            return None
        except Exception as e:
            print(f"❌ 接收失败: {e}")
            return None

    def close(self):
        '''关闭连接'''
        if self.socket:
            self.socket.close()
            self.connected = False

def test_enhanced_client():
    '''测试增强客户端'''
    print("=" * 60)
    print("增强WebSocket客户端测试")
    print("=" * 60)

    client = EnhancedWebSocketClient('127.0.0.1', 9200)

    try:
        if not client.connect():
            return False

        time.sleep(1)

        # 发送用户绑定消息
        message = {
            "route": "/user/bind",
            "userId": "test_user_123",
            "token": "simple_test_token_456"
        }

        message_str = json.dumps(message)

        if client.send_message(message_str):
            print("\n📥 等待服务器响应...")
            response = client.receive_message(timeout=15)

            if response:
                print(f"🎉 收到响应: {response}")
                return True
            else:
                print("❌ 没有收到响应")
                return False
        else:
            return False

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False
    finally:
        client.close()

if __name__ == '__main__':
    test_enhanced_client()
