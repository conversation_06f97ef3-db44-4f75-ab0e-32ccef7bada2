#!/usr/bin/env python
# -*- coding=utf-8 -*-
'''
停止所有直播间服务的脚本
'''

import os
import sys
import psutil
import time

def main():
    print("=" * 60)
    print("停止所有直播间服务")
    print("=" * 60)
    
    # 项目路径配置
    current_dir = os.path.dirname(os.path.abspath(__file__))
    bin_dir = os.path.join(current_dir, 'deploy', 'bin')
    run_py = os.path.join(bin_dir, 'tomato', 'run.py')
    
    # 服务列表
    services = ['CO000001', 'US000001', 'HT000001', 'RM000001']
    
    print("正在查找运行中的服务...")
    
    stopped_count = 0
    
    # 查找并停止相关进程
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            cmdline = ' '.join(proc.info['cmdline']) if proc.info['cmdline'] else ''
            
            # 检查是否是我们的服务进程
            if 'python' in proc.info['name'].lower() and run_py in cmdline:
                for service_id in services:
                    if service_id in cmdline:
                        print(f"发现服务 {service_id} (PID: {proc.info['pid']})")
                        try:
                            proc.terminate()
                            print(f"  ✅ 已发送停止信号给 {service_id}")
                            stopped_count += 1
                        except Exception as e:
                            print(f"  ❌ 停止 {service_id} 失败: {e}")
                        break
        except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
            pass
    
    if stopped_count > 0:
        print(f"\n等待服务停止...")
        time.sleep(3)
        
        # 检查是否还有进程在运行
        still_running = []
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                cmdline = ' '.join(proc.info['cmdline']) if proc.info['cmdline'] else ''
                if 'python' in proc.info['name'].lower() and run_py in cmdline:
                    for service_id in services:
                        if service_id in cmdline:
                            still_running.append((service_id, proc.info['pid']))
                            break
            except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                pass
        
        if still_running:
            print("\n以下服务仍在运行，尝试强制停止:")
            for service_id, pid in still_running:
                try:
                    proc = psutil.Process(pid)
                    proc.kill()
                    print(f"  ✅ 强制停止 {service_id} (PID: {pid})")
                except Exception as e:
                    print(f"  ❌ 强制停止 {service_id} 失败: {e}")
        
        print(f"\n✅ 已停止 {stopped_count} 个服务")
    else:
        print("没有发现运行中的服务")
    
    print("\n" + "=" * 60)
    print("服务停止完成！")
    print("=" * 60)

if __name__ == '__main__':
    main()
