#!/usr/bin/env python
# -*- coding=utf-8 -*-
'''
模拟客户端连接测试 - 连接到 /ws 路径
'''

import asyncio
import websockets
import json
import time

async def test_client_websocket():
    """测试客户端WebSocket连接"""
    # 模拟客户端连接地址
    uri = "ws://127.0.0.1:9200/ws"  # 注意这里是 /ws 路径
    
    print("=" * 60)
    print("模拟客户端WebSocket连接测试")
    print("=" * 60)
    print(f"连接地址: {uri}")
    print("模拟域名: dudupy.yuyan sound.com")
    
    try:
        print("\n正在连接WebSocket服务器...")
        
        # 添加自定义头部，模拟真实客户端
        extra_headers = {
            "Origin": "http://dudupy.yuyan sound.com",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
        }
        
        # 连接WebSocket服务器
        async with websockets.connect(uri, timeout=10, extra_headers=extra_headers) as websocket:
            print("✅ WebSocket连接成功!")
            print(f"连接状态: {websocket.state}")
            print(f"本地地址: {websocket.local_address}")
            print(f"远程地址: {websocket.remote_address}")
            
            # 步骤1: 用户绑定 - 使用正确的消息格式
            print(f"\n--- 步骤1: 用户绑定 ---")
            bind_message = {
                "msgType": 1,  # TYPE_REQUEST
                "msgId": 1001,
                "route": "/user/bind",
                "body": {
                    "userId": "100216",
                    "token": "token_100216_test"
                }
            }
            
            message_str = json.dumps(bind_message, ensure_ascii=False)
            print(f"发送用户绑定: {message_str}")
            
            await websocket.send(message_str)
            print("✅ 用户绑定消息发送成功")
            
            try:
                response = await asyncio.wait_for(websocket.recv(), timeout=10.0)
                print(f"✅ 收到用户绑定响应: {response}")
                
                try:
                    response_data = json.loads(response)
                    print("解析后的绑定响应:")
                    print(json.dumps(response_data, indent=2, ensure_ascii=False))
                    
                    # 检查响应状态
                    if response_data.get("ec") == 0:
                        print("✅ 用户绑定成功!")
                    else:
                        print(f"⚠️  用户绑定失败: {response_data.get('info', '未知错误')}")
                        
                except json.JSONDecodeError:
                    print(f"绑定响应不是JSON格式: {response}")
                    
            except asyncio.TimeoutError:
                print("⚠️  用户绑定响应超时")
            
            await asyncio.sleep(2)  # 等待2秒
            
            # 步骤2: 绑定并进入房间100216
            print(f"\n--- 步骤2: 绑定并进入房间100216 ---")
            enter_room_message = {
                "msgType": 1,  # TYPE_REQUEST
                "msgId": 1002,
                "route": "/user/bindAndEnterRoom",
                "body": {
                    "roomId": "100216",
                    "authToken": "token_100216_test",
                    "platform": "web",
                    "version": "1.0.0"
                }
            }
            
            message_str = json.dumps(enter_room_message, ensure_ascii=False)
            print(f"发送进入房间: {message_str}")
            
            await websocket.send(message_str)
            print("✅ 进入房间消息发送成功")
            
            try:
                response = await asyncio.wait_for(websocket.recv(), timeout=10.0)
                print(f"✅ 收到进入房间响应: {response}")
                
                try:
                    response_data = json.loads(response)
                    print("解析后的进入房间响应:")
                    print(json.dumps(response_data, indent=2, ensure_ascii=False))
                    
                    # 检查响应状态
                    if response_data.get("ec") == 0:
                        print("✅ 进入房间成功!")
                        room_info = response_data.get("result", {})
                        print(f"房间信息: {room_info}")
                    else:
                        print(f"⚠️  进入房间失败: {response_data.get('info', '未知错误')}")
                        
                except json.JSONDecodeError:
                    print(f"进入房间响应不是JSON格式: {response}")
                    
            except asyncio.TimeoutError:
                print("⚠️  进入房间响应超时")
            
            await asyncio.sleep(2)  # 等待2秒
            
            # 步骤3: 调用Python接口示例
            print(f"\n--- 步骤3: 调用Python接口示例 ---")
            
            # 示例：获取房间信息
            api_call_message = {
                "msgType": 1,  # TYPE_REQUEST
                "msgId": 1003,
                "route": "/room/getRoomInfo",
                "body": {
                    "roomId": "100216"
                }
            }
            
            message_str = json.dumps(api_call_message, ensure_ascii=False)
            print(f"发送API调用: {message_str}")
            
            await websocket.send(message_str)
            print("✅ API调用消息发送成功")
            
            try:
                response = await asyncio.wait_for(websocket.recv(), timeout=10.0)
                print(f"✅ 收到API响应: {response}")
                
                try:
                    response_data = json.loads(response)
                    print("解析后的API响应:")
                    print(json.dumps(response_data, indent=2, ensure_ascii=False))
                except json.JSONDecodeError:
                    print(f"API响应不是JSON格式: {response}")
                    
            except asyncio.TimeoutError:
                print("⚠️  API调用响应超时")
            
            # 步骤4: 保持连接，监听服务器推送
            print(f"\n--- 步骤4: 监听服务器推送消息 ---")
            print("保持连接20秒，监听服务器推送...")
            
            try:
                for i in range(20):
                    try:
                        message = await asyncio.wait_for(websocket.recv(), timeout=1.0)
                        print(f"\n📨 收到服务器推送: {message}")
                        
                        # 尝试解析推送消息
                        try:
                            msg_data = json.loads(message)
                            print("解析后的推送消息:")
                            print(json.dumps(msg_data, indent=2, ensure_ascii=False))
                        except json.JSONDecodeError:
                            print(f"推送消息不是JSON格式: {message}")
                            
                    except asyncio.TimeoutError:
                        print(f".", end="", flush=True)
                    await asyncio.sleep(1)
                    
            except websockets.exceptions.ConnectionClosed:
                print("\n⚠️  连接被服务器关闭")
            
            print(f"\n\n✅ 客户端连接测试完成")
            
    except websockets.exceptions.InvalidURI:
        print("❌ 无效的WebSocket URI")
    except websockets.exceptions.InvalidHandshake as e:
        print(f"❌ WebSocket握手失败: {e}")
        print("可能的原因:")
        print("1. 服务器不支持 /ws 路径")
        print("2. 服务器配置问题")
    except ConnectionRefusedError:
        print("❌ 连接被拒绝")
        print("请确保WebSocket服务正在运行:")
        print("python start_websocket_service.py")
    except asyncio.TimeoutError:
        print("❌ 连接超时")
    except Exception as e:
        print(f"❌ 连接失败: {e}")
        print(f"错误类型: {type(e).__name__}")

def setup_hosts_file():
    """设置hosts文件，将域名指向本地"""
    print("=" * 60)
    print("域名配置说明")
    print("=" * 60)
    print("为了测试域名连接，您需要在hosts文件中添加:")
    print("127.0.0.1    dudupy.yuyan sound.com")
    print("")
    print("Windows hosts文件位置:")
    print("C:\\Windows\\System32\\drivers\\etc\\hosts")
    print("")
    print("添加后可以使用域名连接:")
    print("ws://dudupy.yuyan sound.com:9200/ws")

async def test_domain_connection():
    """测试域名连接"""
    uri = "ws://dudupy.yuyan sound.com:9200/ws"
    
    print(f"\n尝试连接域名: {uri}")
    
    try:
        async with websockets.connect(uri, timeout=5) as websocket:
            print("✅ 域名连接成功!")
            return True
    except Exception as e:
        print(f"❌ 域名连接失败: {e}")
        print("请检查hosts文件配置")
        return False

async def main():
    """主函数"""
    setup_hosts_file()
    
    # 先测试本地IP连接
    await test_client_websocket()
    
    # 然后测试域名连接
    print("\n" + "=" * 60)
    print("测试域名连接")
    print("=" * 60)
    await test_domain_connection()

if __name__ == '__main__':
    try:
        import websockets
        print("开始模拟客户端连接测试...")
        asyncio.run(main())
    except ImportError:
        print("websockets库未安装")
        print("pip install websockets")
