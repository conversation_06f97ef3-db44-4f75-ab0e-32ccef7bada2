#!/usr/bin/env python
# -*- coding=utf-8 -*-
'''
直播间服务管理器 - 主菜单
'''

import os
import sys
import subprocess

def run_script(script_name):
    """运行指定的脚本"""
    try:
        subprocess.run([sys.executable, script_name], check=True)
    except subprocess.CalledProcessError as e:
        print(f"脚本执行失败: {e}")
    except FileNotFoundError:
        print(f"脚本文件不存在: {script_name}")

def main():
    while True:
        print("\n" + "=" * 60)
        print("直播间服务管理器")
        print("=" * 60)
        print("1. 🚀 启动所有服务 (新窗口)")
        print("2. 🖥️  启动所有服务 (当前终端)")
        print("3. 🛑 停止所有服务")
        print("4. 📊 检查服务状态")
        print("5. 🔧 启动单个服务")
        print("6. 🧪 测试环境")
        print("7. 📝 查看日志")
        print("8. ❓ 帮助信息")
        print("9. 🚪 退出")
        print("=" * 60)
        
        choice = input("请选择操作 (1-8): ").strip()
        
        if choice == '1':
            print("\n启动所有服务...")
            run_script('start_all_services.py')
            
        elif choice == '2':
            print("\n停止所有服务...")
            run_script('stop_all_services.py')
            
        elif choice == '3':
            print("\n检查服务状态...")
            run_script('check_services.py')
            
        elif choice == '4':
            print("\n可用的服务:")
            services = [
                ('CO000001', '连接服务器'),
                ('US000001', '用户服务器'),
                ('HT000001', 'HTTP服务器'),
                ('RM000001', '房间服务器')
            ]
            
            for i, (service_id, name) in enumerate(services, 1):
                print(f"  {i}. {name} ({service_id})")
            
            try:
                service_choice = int(input("选择要启动的服务 (1-4): ")) - 1
                if 0 <= service_choice < len(services):
                    service_id = services[service_choice][0]
                    run_script(f'start_single_service.py {service_id}')
                else:
                    print("无效的选择")
            except ValueError:
                print("请输入有效的数字")
                
        elif choice == '5':
            print("\n测试环境...")
            run_script('check_environment.py')
            
        elif choice == '6':
            print("\n查看日志文件...")
            logs_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'deploy', 'logs')
            
            if os.path.exists(logs_path):
                log_files = [f for f in os.listdir(logs_path) if f.endswith('.log')]
                if log_files:
                    print("可用的日志文件:")
                    for i, log_file in enumerate(log_files, 1):
                        print(f"  {i}. {log_file}")
                    
                    try:
                        log_choice = int(input("选择要查看的日志 (输入数字): ")) - 1
                        if 0 <= log_choice < len(log_files):
                            log_file_path = os.path.join(logs_path, log_files[log_choice])
                            print(f"\n{log_files[log_choice]} 内容:")
                            print("-" * 60)
                            try:
                                with open(log_file_path, 'r', encoding='utf-8') as f:
                                    lines = f.readlines()
                                    # 显示最后20行
                                    for line in lines[-20:]:
                                        print(line.strip())
                            except Exception as e:
                                print(f"读取日志文件失败: {e}")
                        else:
                            print("无效的选择")
                    except ValueError:
                        print("请输入有效的数字")
                else:
                    print("没有找到日志文件")
            else:
                print("日志目录不存在")
                
        elif choice == '7':
            print("\n" + "=" * 60)
            print("帮助信息")
            print("=" * 60)
            print("这是一个直播间服务管理器，包含以下功能:")
            print()
            print("🚀 启动所有服务:")
            print("   一次性启动所有4个微服务，每个服务在独立窗口运行")
            print()
            print("🛑 停止所有服务:")
            print("   查找并停止所有正在运行的服务进程")
            print()
            print("📊 检查服务状态:")
            print("   检查进程状态、端口状态、服务响应和日志文件")
            print()
            print("🔧 启动单个服务:")
            print("   选择性启动单个服务，用于调试")
            print()
            print("🧪 测试环境:")
            print("   检查Python环境、依赖库、Redis连接等")
            print()
            print("📝 查看日志:")
            print("   查看各个服务的运行日志")
            print()
            print("服务说明:")
            print("- CO000001: 连接服务器 (WebSocket端口9200)")
            print("- US000001: 用户服务器")
            print("- HT000001: HTTP服务器 (端口9000)")
            print("- RM000001: 房间服务器")
            print()
            print("访问地址:")
            print("- HTTP API: http://127.0.0.1:9000/")
            print("- WebSocket: ws://127.0.0.1:9200/")
            print("=" * 60)
            
        elif choice == '8':
            print("\n感谢使用直播间服务管理器！")
            break
            
        else:
            print("\n无效的选择，请重新输入")
        
        if choice != '8':
            input("\n按回车键继续...")

if __name__ == '__main__':
    main()
