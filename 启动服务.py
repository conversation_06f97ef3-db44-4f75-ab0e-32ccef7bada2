#!/usr/bin/env python
# -*- coding=utf-8 -*-
'''
使用Python 3.8启动所有服务的最简单脚本
'''

import os
import sys
import subprocess

# 配置路径
current_dir = os.path.dirname(os.path.abspath(__file__))
deploy_path = os.path.join(current_dir, 'deploy')
bin_dir = os.path.join(deploy_path, 'bin')
config_path = os.path.join(deploy_path, 'config')
logs_path = os.path.join(deploy_path, 'logs')

# 确保日志目录存在
os.makedirs(logs_path, exist_ok=True)

# 首先创建一个模拟的stackless模块
stackless_dir = os.path.join(bin_dir, 'stackless_mock')
os.makedirs(stackless_dir, exist_ok=True)

# 创建模拟的stackless模块
with open(os.path.join(stackless_dir, '__init__.py'), 'w') as f:
    f.write('''
# 模拟stackless模块
class bomb(Exception):
    pass

class tasklet(object):
    def __init__(self, func=None, *args, **kwargs):
        self.func = func
        self.args = args
        self.kwargs = kwargs
    
    def __call__(self, *args, **kwargs):
        return self
    
    def setup(self, *args, **kwargs):
        return self
    
    def insert(self):
        return self
    
    def remove(self):
        return self

def schedule(*args, **kwargs):
    pass

def run(*args, **kwargs):
    pass

def getcurrent(*args, **kwargs):
    return tasklet()
''')

# 使用Python 3.8解释器路径
python_path = sys.executable  # 使用当前运行的Python解释器

# 服务ID列表
services = [
    'CO000001',  # 连接服务器
    'US000001',  # 用户服务器
    'HT000001',  # HTTP服务器
    'RM000001'   # 房间服务器
]

def start_service(service_id, reload='0'):
    """启动指定的服务"""
    env = dict(os.environ)
    env["PYTHONPATH"] = f"{stackless_dir};{bin_dir}"
    
    # 创建一个启动脚本
    startup_script = os.path.join(current_dir, f"start_{service_id}.py")
    with open(startup_script, 'w') as f:
        f.write(f'''
import sys
import os

# 添加模拟的stackless模块到路径
sys.path.insert(0, r"{stackless_dir}")
sys.path.insert(1, r"{bin_dir}")

# 导入必要的模块
from tomato.server import server

# 运行服务
server.runWithFileConf("{service_id}", r"{config_path}", __import__("fqparty").app, [])
''')
    
    cmd = [
        python_path,
        startup_script
    ]
    
    print(f"启动服务 {service_id}...")
    
    # 使用新的控制台窗口启动
    process = subprocess.Popen(
        cmd,
        env=env,
        creationflags=subprocess.CREATE_NEW_CONSOLE
    )
    
    return process

if __name__ == '__main__':
    print("开始启动所有服务...")
    
    processes = []
    
    for service_id in services:
        process = start_service(service_id)
        processes.append((service_id, process))
    
    print("所有服务已启动！")
    print("每个服务都在独立的控制台窗口中运行")
    print("关闭相应的控制台窗口可以停止服务")
    
    # 等待用户输入以保持脚本运行
    input("按回车键退出此启动脚本...") 