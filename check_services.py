#!/usr/bin/env python
# -*- coding=utf-8 -*-
'''
检查所有直播间服务状态的脚本
'''

import os
import sys
import psutil
import socket
import urllib.request

def check_port(host, port):
    """检查端口是否开放"""
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.settimeout(1)
            result = s.connect_ex((host, port))
            return result == 0
    except:
        return False

def check_http_service():
    """检查HTTP服务"""
    try:
        response = urllib.request.urlopen('http://127.0.0.1:9000/', timeout=5)
        return True, f"HTTP {response.getcode()}"
    except Exception as e:
        return False, str(e)

def main():
    print("=" * 60)
    print("直播间服务状态检查")
    print("=" * 60)
    
    # 项目路径配置
    current_dir = os.path.dirname(os.path.abspath(__file__))
    bin_dir = os.path.join(current_dir, 'deploy', 'bin')
    logs_path = os.path.join(current_dir, 'deploy', 'logs')
    run_py = os.path.join(bin_dir, 'tomato', 'run.py')
    
    # 服务配置
    services = {
        'CO000001': {'name': '连接服务器', 'port': 9200},
        'US000001': {'name': '用户服务器', 'port': None},
        'HT000001': {'name': 'HTTP服务器', 'port': 9000},
        'RM000001': {'name': '房间服务器', 'port': None}
    }
    
    print("1. 检查进程状态:")
    print("-" * 40)
    
    running_services = {}
    
    # 检查进程
    for proc in psutil.process_iter(['pid', 'name', 'cmdline', 'create_time']):
        try:
            cmdline = ' '.join(proc.info['cmdline']) if proc.info['cmdline'] else ''
            
            if 'python' in proc.info['name'].lower() and run_py in cmdline:
                for service_id in services:
                    if service_id in cmdline:
                        running_services[service_id] = {
                            'pid': proc.info['pid'],
                            'start_time': proc.info['create_time']
                        }
                        print(f"  ✅ {services[service_id]['name']} ({service_id}) - PID: {proc.info['pid']}")
                        break
        except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
            pass
    
    # 显示未运行的服务
    for service_id, config in services.items():
        if service_id not in running_services:
            print(f"  ❌ {config['name']} ({service_id}) - 未运行")
    
    print("\n2. 检查端口状态:")
    print("-" * 40)
    
    # 检查端口
    for service_id, config in services.items():
        if config['port']:
            port_open = check_port('127.0.0.1', config['port'])
            status = "✅ 开放" if port_open else "❌ 关闭"
            print(f"  端口 {config['port']} ({config['name']}): {status}")
    
    print("\n3. 检查服务响应:")
    print("-" * 40)
    
    # 检查HTTP服务
    http_ok, http_msg = check_http_service()
    http_status = "✅ 正常" if http_ok else "❌ 异常"
    print(f"  HTTP服务 (http://127.0.0.1:9000/): {http_status}")
    if not http_ok:
        print(f"    错误: {http_msg}")
    
    print("\n4. 检查日志文件:")
    print("-" * 40)
    
    # 检查日志文件
    for service_id, config in services.items():
        log_file = os.path.join(logs_path, f"{service_id}.log")
        if os.path.exists(log_file):
            try:
                # 获取文件大小和修改时间
                stat = os.stat(log_file)
                size_kb = stat.st_size / 1024
                print(f"  ✅ {service_id}.log - {size_kb:.1f}KB")
                
                # 检查最近的错误
                with open(log_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    if 'ERROR' in content or 'Exception' in content:
                        print(f"    ⚠️  发现错误信息")
            except Exception as e:
                print(f"  ❌ {service_id}.log - 读取失败: {e}")
        else:
            print(f"  ❌ {service_id}.log - 文件不存在")
    
    print("\n" + "=" * 60)
    print("状态检查完成！")
    
    # 总结
    total_services = len(services)
    running_count = len(running_services)
    
    if running_count == total_services:
        print("🎉 所有服务都在正常运行！")
    elif running_count > 0:
        print(f"⚠️  {running_count}/{total_services} 个服务在运行")
    else:
        print("❌ 没有服务在运行")
    
    print("=" * 60)

if __name__ == '__main__':
    main()
