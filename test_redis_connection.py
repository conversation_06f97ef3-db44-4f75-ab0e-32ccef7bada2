#!/usr/bin/env python
# -*- coding=utf-8 -*-
'''
测试Redis连接的详细脚本
'''

import os
import sys

def main():
    # 项目路径配置
    current_dir = os.path.dirname(os.path.abspath(__file__))
    bin_dir = os.path.join(current_dir, 'deploy', 'bin')
    config_path = os.path.join(current_dir, 'deploy', 'config')
    
    # 设置路径
    sys.path.insert(0, bin_dir)
    
    print("测试Redis连接...")
    print("=" * 50)
    
    try:
        print("1. 导入模块...")
        import stackless
        from tomato.config import configure
        from tomato.db.redis import client as redisClient
        from tomato.utils import ttlog
        print("   ✅ 模块导入成功")
        
        print("2. 初始化配置...")
        configure.initByFile(config_path)
        print("   ✅ 配置初始化成功")
        
        print("3. 加载Redis配置...")
        redis_conf = configure.loadJson('server.fqparty.redis', {})
        print(f"   Redis配置: {redis_conf}")
        
        print("4. 测试连接各个Redis数据库...")
        for db_name, conf_list in redis_conf.items():
            print(f"\n   测试 {db_name} 数据库:")
            for i, conf in enumerate(conf_list):
                print(f"     配置 {i+1}: {conf}")
                try:
                    # 使用简单的redis库测试连接
                    import redis
                    r = redis.Redis(
                        host=conf['host'], 
                        port=conf['port'], 
                        db=conf['db'], 
                        password=conf.get('password') or None,
                        socket_timeout=5
                    )
                    r.ping()
                    print(f"     ✅ 连接成功")
                    
                    # 现在测试tomato的Redis客户端
                    print(f"     测试tomato Redis客户端...")
                    conn = redisClient.connectRedis(
                        conf['host'], 
                        conf['port'], 
                        conf['db'], 
                        conf.get('password'),
                        timeout=10
                    )
                    print(f"     ✅ tomato Redis客户端连接成功")
                    redisClient.closeRedis(conn)
                    
                except Exception as e:
                    print(f"     ❌ 连接失败: {e}")
                    import traceback
                    traceback.print_exc()
        
        print("\n5. 测试connectRedisCluster...")
        try:
            session_conf = redis_conf.get('session', [])
            if session_conf:
                conn_list = redisClient.connectRedisCluster(session_conf, 10, 3)
                print(f"   ✅ connectRedisCluster成功，连接数: {len(conn_list)}")
                for conn in conn_list:
                    redisClient.closeRedis(conn)
            else:
                print("   ⚠️  没有session配置")
        except Exception as e:
            print(f"   ❌ connectRedisCluster失败: {e}")
            import traceback
            traceback.print_exc()
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
