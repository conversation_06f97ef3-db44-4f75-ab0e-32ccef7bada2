#!/usr/bin/env python
# -*- coding=utf-8 -*-
'''
使用正确参数测试WebSocket连接 - 确保收到服务器响应
'''

import asyncio
import websockets
import json
import time

async def test_with_correct_params():
    """使用正确参数测试，确保收到响应"""
    uri = "ws://127.0.0.1:9200/"
    
    print("=" * 60)
    print("使用正确参数测试WebSocket连接")
    print("=" * 60)
    print(f"连接地址: {uri}")
    print("使用真实token: 072d576456d38bfc41208c404fc3c087")
    print("目标房间: 100216")
    
    try:
        async with websockets.connect(uri, timeout=10) as websocket:
            print("\n✅ WebSocket连接成功!")
            print(f"连接状态: {websocket.state}")
            
            # 测试1: 用户绑定 - 使用正确的token
            print(f"\n--- 测试1: 用户绑定 ---")
            bind_message = {
                "route": "/user/bind",
                "userId": "100216",
                "token": "072d576456d38bfc41208c404fc3c087"
            }
            
            message_str = json.dumps(bind_message)
            print(f"发送: {message_str}")
            
            await websocket.send(message_str)
            print("✅ 消息发送成功")
            
            try:
                response = await asyncio.wait_for(websocket.recv(), timeout=10.0)
                print(f"✅ 收到响应: {response}")
                
                try:
                    response_data = json.loads(response)
                    print("解析后的响应:")
                    print(json.dumps(response_data, indent=2, ensure_ascii=False))
                    
                    if response_data.get("ec") == 0:
                        print("🎉 用户绑定成功!")
                    else:
                        print(f"⚠️  绑定失败: {response_data.get('info', '未知错误')}")
                        
                except json.JSONDecodeError:
                    print(f"响应不是JSON格式: {response}")
                    
            except asyncio.TimeoutError:
                print("⚠️  用户绑定响应超时")
            
            await asyncio.sleep(2)
            
            # 测试2: 绑定并进入房间 - 使用正确的参数
            print(f"\n--- 测试2: 绑定并进入房间100216 ---")
            enter_room_message = {
                "route": "/user/bindAndEnterRoom",
                "roomId": 100216,
                "authToken": "072d576456d38bfc41208c404fc3c087",
                "platform": "iOS,18.3",
                "version": "5.0.0",
                "isReceivePush": 1,
                "onMicForLogout": 1
            }
            
            message_str = json.dumps(enter_room_message)
            print(f"发送: {message_str}")
            
            await websocket.send(message_str)
            print("✅ 消息发送成功")
            
            try:
                response = await asyncio.wait_for(websocket.recv(), timeout=10.0)
                print(f"✅ 收到响应: {response}")
                
                try:
                    response_data = json.loads(response)
                    print("解析后的响应:")
                    print(json.dumps(response_data, indent=2, ensure_ascii=False))
                    
                    if response_data.get("ec") == 0:
                        print("🎉 进入房间成功!")
                        room_info = response_data.get("result", {})
                        if room_info:
                            print(f"房间信息: {room_info}")
                    else:
                        print(f"⚠️  进入房间失败: {response_data.get('info', '未知错误')}")
                        
                except json.JSONDecodeError:
                    print(f"响应不是JSON格式: {response}")
                    
            except asyncio.TimeoutError:
                print("⚠️  进入房间响应超时")
            
            await asyncio.sleep(2)
            
            # 测试3: 尝试不同的消息格式
            print(f"\n--- 测试3: 尝试简化格式 ---")
            simple_message = {
                "route": "/user/bindAndEnterRoom",
                "roomId": "100216",  # 字符串格式
                "authToken": "072d576456d38bfc41208c404fc3c087"
            }
            
            message_str = json.dumps(simple_message)
            print(f"发送: {message_str}")
            
            await websocket.send(message_str)
            print("✅ 消息发送成功")
            
            try:
                response = await asyncio.wait_for(websocket.recv(), timeout=10.0)
                print(f"✅ 收到响应: {response}")
                
                try:
                    response_data = json.loads(response)
                    print("解析后的响应:")
                    print(json.dumps(response_data, indent=2, ensure_ascii=False))
                except json.JSONDecodeError:
                    print(f"响应不是JSON格式: {response}")
                    
            except asyncio.TimeoutError:
                print("⚠️  简化格式响应超时")
            
            # 测试4: 监听服务器推送消息
            print(f"\n--- 测试4: 监听服务器推送 ---")
            print("保持连接15秒，监听服务器推送消息...")
            
            try:
                for i in range(15):
                    try:
                        message = await asyncio.wait_for(websocket.recv(), timeout=1.0)
                        print(f"\n📨 收到服务器推送: {message}")
                        
                        try:
                            msg_data = json.loads(message)
                            print("解析后的推送消息:")
                            print(json.dumps(msg_data, indent=2, ensure_ascii=False))
                        except json.JSONDecodeError:
                            print(f"推送消息不是JSON格式: {message}")
                            
                    except asyncio.TimeoutError:
                        print(".", end="", flush=True)
                    await asyncio.sleep(1)
                    
            except websockets.exceptions.ConnectionClosed:
                print("\n⚠️  连接被服务器关闭")
            
            print(f"\n\n✅ 测试完成")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

def check_token_in_redis():
    """检查Redis中的token"""
    print("=" * 60)
    print("检查Redis中的token")
    print("=" * 60)
    
    try:
        import redis
        r = redis.Redis(host='127.0.0.1', port=6379, db=1)
        
        # 检查token
        token = "072d576456d38bfc41208c404fc3c087"
        user_id = r.get(token)
        
        if user_id:
            print(f"✅ Token验证成功: {token} -> {user_id.decode()}")
            return True
        else:
            print(f"❌ Token不存在: {token}")
            return False
            
    except Exception as e:
        print(f"❌ Redis检查失败: {e}")
        return False

async def main():
    """主函数"""
    # 先检查token
    if not check_token_in_redis():
        print("\n请先运行: python setup_test_token.py")
        return
    
    # 然后测试连接
    await test_with_correct_params()

if __name__ == '__main__':
    try:
        import websockets
        print("开始使用正确参数测试...")
        asyncio.run(main())
    except ImportError:
        print("websockets库未安装")
        print("pip install websockets")
