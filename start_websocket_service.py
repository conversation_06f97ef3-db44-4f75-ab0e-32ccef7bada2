#!/usr/bin/env python
# -*- coding=utf-8 -*-
'''
启动WebSocket连接服务 (CO000001)
'''

import os
import sys
import time
import socket

def main():
    print("=" * 60)
    print("启动WebSocket连接服务 (CO000001)")
    print("=" * 60)
    
    # 项目路径配置
    current_dir = os.path.dirname(os.path.abspath(__file__))
    bin_dir = os.path.join(current_dir, 'deploy', 'bin')
    config_path = os.path.join(current_dir, 'deploy', 'config')
    logs_path = os.path.join(current_dir, 'deploy', 'logs')
    
    # 设置路径
    sys.path.insert(0, bin_dir)
    
    def check_port(port):
        """检查端口是否在监听"""
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.settimeout(1)
                result = s.connect_ex(('127.0.0.1', port))
                return result == 0
        except:
            return False
    
    try:
        print("1. 导入模块...")
        import stackless
        import tomato
        from tomato.config import configure
        from tomato.utils import ttlog
        import fqparty
        print("   [OK] 模块导入成功")
        
        print("2. 初始化配置...")
        configure.initByFile(config_path)
        print("   [OK] 配置初始化成功")
        
        print("3. 设置日志级别...")
        logLevel = ttlog.INFO
        ttlog.setLevel(logLevel)
        print(f"   [OK] 日志级别: {logLevel}")
        
        print("4. 初始化tomato应用...")
        tomato.app.appArgs = []
        tomato.app.init('CO000001')  # WebSocket连接服务
        print("   [OK] tomato应用初始化成功")
        
        print("5. 初始化fqparty应用...")
        fqparty.app.init()
        print("   [OK] fqparty应用初始化成功")
        
        print("6. 启动fqparty应用...")
        fqparty.app.start()
        print("   [OK] fqparty应用启动成功")
        
        print("7. 启动tomato应用...")
        tomato.app.start()
        print("   [OK] tomato应用启动成功")
        
        print("\n" + "=" * 60)
        print("WebSocket连接服务启动成功!")
        print("=" * 60)
        print("WebSocket地址: ws://127.0.0.1:9200/")
        print("按 Ctrl+C 停止服务")
        print("=" * 60)
        
        # 检查端口是否在监听
        print("\n检查端口状态...")
        for i in range(10):
            if check_port(9200):
                print(f"[OK] 端口9200正在监听")
                break
            else:
                print(f"等待端口9200开始监听... ({i+1}/10)")
                time.sleep(1)
        else:
            print("[WARNING] 端口9200未开始监听")
        
        # 显示服务配置信息
        print("\n服务配置信息:")
        server_conf = configure.loadJson('server.tomato.servers', {}).get('CO000001', {})
        if server_conf:
            frontend = server_conf.get('frontend', {})
            listenings = frontend.get('listenings', {})
            ws_conf = listenings.get('ws', {})
            print(f"- 最大连接数: {frontend.get('maxConnection', 'N/A')}")
            print(f"- WebSocket端口: {ws_conf.get('port', 'N/A')}")
            print(f"- WebSocket参数: {ws_conf.get('params', {})}")
        
        # 保持服务运行
        print("\n服务运行中...")
        try:
            while True:
                time.sleep(5)
                if not check_port(9200):
                    print("[WARNING] 端口9200不再监听")
                    break
                print(".", end="", flush=True)
        except KeyboardInterrupt:
            print("\n\nWebSocket服务已停止")
        
    except Exception as e:
        print(f"\n[ERROR] 启动失败: {e}")
        import traceback
        traceback.print_exc()
        input("\n按回车键退出...")

if __name__ == '__main__':
    main()
