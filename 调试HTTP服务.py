#!/usr/bin/env python
# -*- coding=utf-8 -*-
'''
调试HTTP服务器
'''

import os
import sys
import subprocess
import time

# 配置路径
current_dir = os.path.dirname(os.path.abspath(__file__))
deploy_path = os.path.join(current_dir, 'deploy')
bin_dir = os.path.join(deploy_path, 'bin')
config_path = os.path.join(deploy_path, 'config')
logs_path = os.path.join(deploy_path, 'logs')

# 确保日志目录存在
os.makedirs(logs_path, exist_ok=True)

# 创建模拟的stackless模块
stackless_dir = os.path.join(bin_dir, 'stackless_mock')
os.makedirs(stackless_dir, exist_ok=True)

with open(os.path.join(stackless_dir, '__init__.py'), 'w') as f:
    f.write('''
# 模拟stackless模块
class bomb(Exception):
    pass

class tasklet(object):
    def __init__(self, func=None, *args, **kwargs):
        self.func = func
        self.args = args
        self.kwargs = kwargs
    
    def __call__(self, *args, **kwargs):
        return self
    
    def setup(self, *args, **kwargs):
        return self
    
    def insert(self):
        return self
    
    def remove(self):
        return self

def schedule(*args, **kwargs):
    pass

def run(*args, **kwargs):
    pass

def getcurrent(*args, **kwargs):
    return tasklet()
''')

# 检查Redis是否运行
def check_redis():
    try:
        import socket
        s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        s.settimeout(1)
        s.connect(('127.0.0.1', 6379))
        s.close()
        return True
    except:
        return False

# 检查端口是否被占用
def check_port_in_use(port):
    import socket
    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
        return s.connect_ex(('127.0.0.1', port)) == 0

# 启动HTTP服务
def start_http_service():
    service_id = 'HT000001'
    
    # 检查端口
    if check_port_in_use(9000):
        print("警告：端口9000已被占用！请关闭占用该端口的程序后重试。")
        return None
    
    # 检查Redis
    if not check_redis():
        print("警告：Redis服务未启动或无法连接！请确保Redis服务正在运行。")
        print("提示：您可以从 https://github.com/microsoftarchive/redis/releases 下载Windows版Redis")
        return None
    
    # 创建更详细的启动脚本
    startup_script = os.path.join(current_dir, f"debug_{service_id}.py")
    with open(startup_script, 'w') as f:
        f.write(f'''
import sys
import os
import traceback

# 设置更详细的日志
import logging
logging.basicConfig(level=logging.DEBUG, 
                   format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('debug_http')

try:
    # 添加模拟的stackless模块到路径
    logger.debug("添加路径: %s", r"{stackless_dir}")
    sys.path.insert(0, r"{stackless_dir}")
    
    logger.debug("添加路径: %s", r"{bin_dir}")
    sys.path.insert(1, r"{bin_dir}")
    
    # 导入必要的模块
    logger.debug("开始导入server模块")
    from tomato.server import server
    logger.debug("成功导入server模块")
    
    # 检查配置路径
    logger.debug("配置路径: %s", r"{config_path}")
    if not os.path.exists(r"{config_path}"):
        logger.error("配置路径不存在!")
    else:
        logger.debug("配置路径存在，内容: %s", os.listdir(r"{config_path}"))
    
    # 检查服务器配置
    server_config = os.path.join(r"{config_path}", "server/tomato/servers.json")
    if os.path.exists(server_config):
        with open(server_config, 'r', encoding='utf-8') as f:
            logger.debug("服务器配置: %s", f.read())
    
    # 检查Redis连接
    try:
        import socket
        s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        s.settimeout(1)
        s.connect(('127.0.0.1', 6379))
        s.close()
        logger.debug("Redis连接成功")
    except:
        logger.error("Redis连接失败!")
    
    # 运行服务
    logger.debug("开始启动HTTP服务: {service_id}")
    server.runWithFileConf("{service_id}", r"{config_path}", __import__("fqparty").app, [])
except Exception as e:
    logger.error("启动过程中出错: %s", str(e))
    logger.error(traceback.format_exc())
    input("按回车键退出...")
''')
    
    # 使用当前Python解释器
    python_path = sys.executable
    
    env = dict(os.environ)
    env["PYTHONPATH"] = f"{stackless_dir};{bin_dir}"
    
    print(f"启动HTTP服务 {service_id}...")
    print(f"使用Python解释器: {python_path}")
    print(f"启动脚本: {startup_script}")
    
    # 启动服务
    process = subprocess.Popen(
        [python_path, startup_script],
        env=env,
        creationflags=subprocess.CREATE_NEW_CONSOLE
    )
    
    print("HTTP服务启动中，请查看新打开的控制台窗口获取详细日志")
    return process

if __name__ == "__main__":
    print("开始调试HTTP服务...")
    
    # 检查Redis
    if check_redis():
        print("Redis连接正常")
    else:
        print("警告：无法连接到Redis服务！")
    
    # 检查端口
    if check_port_in_use(9000):
        print("警告：端口9000已被占用！")
    else:
        print("端口9000可用")
    
    # 启动HTTP服务
    process = start_http_service()
    
    if process:
        print("\n服务已启动，请查看新打开的控制台窗口获取详细日志")
        print("如果服务启动成功，您应该可以访问 http://127.0.0.1:9000/")
    
    print("\n按回车键退出此脚本...")
    input() 