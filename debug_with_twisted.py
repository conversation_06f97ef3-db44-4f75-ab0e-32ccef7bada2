#!/usr/bin/env python
# -*- coding=utf-8 -*-
'''
在PyCharm中一次性启动所有服务并显示日志，解决http模块冲突问题
'''

import os
import sys
import subprocess
import time
import threading

# 确保系统模块优先于项目模块
# 将标准库路径添加到sys.path的最前面
std_lib_path = os.path.dirname(os.__file__)
if std_lib_path in sys.path:
    sys.path.remove(std_lib_path)
sys.path.insert(0, std_lib_path)

# 安装Twisted依赖
try:
    import twisted
except ImportError:
    print("正在安装Twisted依赖...")
    subprocess.check_call([sys.executable, "-m", "pip", "install", "twisted"])
    print("Twisted安装完成")

# 将bin目录添加到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
bin_dir = os.path.join(current_dir, 'deploy', 'bin')
sys.path.insert(1, bin_dir)  # 注意这里是1，让标准库优先

# 配置路径
deploy_path = os.path.join(current_dir, 'deploy')
config_path = os.path.join(deploy_path, 'config')
logs_path = os.path.join(deploy_path, 'logs')
run_py = os.path.join(bin_dir, 'tomato', 'run.py')

# 确保日志目录存在
os.makedirs(logs_path, exist_ok=True)

# 服务ID列表
services = [
    'CO000001',  # 连接服务器
    'US000001',  # 用户服务器
    'HT000001',  # HTTP服务器
    'RM000001'   # 房间服务器
]

def stream_output(process, service_id):
    """实时输出进程的标准输出和错误输出"""
    for line in iter(process.stdout.readline, ''):
        print(f"[{service_id}] {line.strip()}")
    
    for line in iter(process.stderr.readline, ''):
        print(f"[{service_id} ERROR] {line.strip()}")

def start_service(service_id, reload='0'):
    """启动指定的服务并实时显示输出"""
    # 构建环境变量
    env = dict(os.environ)
    env["PYTHONPATH"] = std_lib_path + os.pathsep + bin_dir
    env["PYTHONIOENCODING"] = "utf-8"
    
    # 使用正确的路径格式
    run_file_path = run_py.replace('\\', '/')
    std_lib_path_fixed = std_lib_path.replace('\\', '/')
    bin_dir_fixed = bin_dir.replace('\\', '/')
    
    cmd = [
        sys.executable,
        "-c",
        f"""
import os, sys
import io
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')
sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding='utf-8')

# 确保标准库优先
sys_path = '{std_lib_path_fixed}'
if sys_path in sys.path:
    sys.path.remove(sys_path)
sys.path.insert(0, sys_path)

# 添加项目路径
bin_path = '{bin_dir_fixed}'
sys.path.insert(1, bin_path)

# 运行服务
run_file = '{run_file_path}'
with open(run_file, 'r', encoding='utf-8') as f:
    code = f.read()
    exec(code)
""",
        service_id,
        'fqparty',
        config_path,
        logs_path,
        reload
    ]
    
    print(f"启动服务 {service_id}...")
    print(f"运行文件路径: {run_file_path}")
    
    # 创建进程，并捕获标准输出和错误输出
    process = subprocess.Popen(
        cmd, 
        stdout=subprocess.PIPE, 
        stderr=subprocess.PIPE,
        env=env,
        bufsize=1,
        universal_newlines=True
    )
    
    # 创建线程来读取输出
    thread = threading.Thread(target=stream_output, args=(process, service_id))
    thread.daemon = True
    thread.start()
    
    return process, thread

if __name__ == '__main__':
    print("开始启动所有服务...")
    
    processes = []
    threads = []
    
    for service_id in services:
        process, thread = start_service(service_id)
        processes.append((service_id, process))
        threads.append(thread)
    
    print("所有服务已启动！")
    print("正在显示实时日志输出...")
    print("按Ctrl+C停止所有服务")
    
    try:
        # 保持脚本运行，同时允许线程继续读取输出
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("正在停止所有服务...")
        for service_id, process in processes:
            print(f"停止服务 {service_id}...")
            process.terminate()
        print("所有服务已停止！") 