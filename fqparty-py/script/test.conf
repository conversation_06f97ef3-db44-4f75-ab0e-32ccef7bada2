{"projectsPath": "/Users/<USER>/zjg/dev/wxapps/server/tomato-py", "compilePath": "/Users/<USER>/zjg/dev/wxapps/server/tomato-py/xyqp-py/script/compile", "configPath": "/Users/<USER>/zjg/dev/wxapps/server/tomato-py/xyqp-py/script/test", "backPath": "/Users/<USER>/zjg/dev/wxapps/server/tomato-py/xyqp-py/script/deploy/back", "deployPath": "/Users/<USER>/zjg/dev/wxapps/server/tomato-py/xyqp-py/script/deploy", "pypy": "/usr/bin/pypy", "configRedis": {"host": "127.0.0.1", "port": 6379, "db": 0}, "appModule": "xyqp", "projects": [{"name": "tomato", "path": "tomato-py", "sources": ["src"]}, {"name": "xysdka<PERSON>", "path": "xysdkapi-py", "sources": ["src"]}, {"name": "xybase", "path": "xybase-py", "sources": ["src"]}, {"name": "xyqp", "path": "xyqp-py", "sources": ["src"]}, {"name": "xyqpgame", "path": "xyqpgame-py", "sources": ["src"]}, {"name": "ddzbase", "path": "ddzbase-py", "sources": ["src"]}, {"name": "qjddz", "path": "qjddz-py", "sources": ["src"]}]}