{"regionId": 1001021, "displayName": "20元红包赛", "openTime": {"desc": "每天10:00~23:00开放", "startTime": "10:00", "stopTime": "23:00"}, "rankLines1": [{"typeId": "rankLine", "rankLine": [[7600, 1], [4700, 5], [3600, 10], [2400, 15], [1200, 20], [800, 25], [600, 30], [400, 35], [200, 40], [0, 45], [-200, 50], [-300, 52], [-400, 54], [-700, 55], [-800, 56], [-900, 57], [-1000, 58], [-1100, 59], [-1200, 60], [-2400, 62], [-3600, 65], [-51500, 69]]}, {"typeId": "tableRank"}], "rankLines": [{"typeId": "rankLine", "rankLine": [[7600, 1], [4700, 5], [3600, 10], [2400, 15], [1200, 20], [800, 25], [600, 30], [400, 35], [200, 40], [0, 45], [-200, 50], [-300, 52], [-400, 54], [-700, 55], [-800, 56], [-900, 57], [-1000, 58], [-1100, 59], [-1200, 60], [-2400, 62], [-3600, 65], [-51500, 69]]}, {"typeId": "rankLine", "rankLine": [[8440, 1], [5260, 5], [3600, 10], [2400, 15], [1200, 20], [600, 23], [400, 25], [200, 30], [0, 35], [-100, 40], [-200, 42], [-400, 43], [-500, 44], [-600, 46], [-800, 48], [-1200, 50], [-51500, 54]]}, {"typeId": "rankLine", "rankLine": [[7990, 1], [5200, 2], [4400, 3], [3600, 4], [2800, 5], [2400, 6], [2000, 7], [1900, 8], [1800, 9], [1700, 10], [1600, 11], [1500, 12], [1400, 13], [1300, 14], [1200, 15], [1100, 16], [1000, 17], [900, 18], [800, 19], [700, 20], [600, 22], [400, 25], [200, 28], [-100, 30], [-200, 32], [-400, 34], [-600, 36], [-1200, 38], [-2400, 40], [-51500, 42]]}, {"typeId": "rankLine", "rankLine": [[7632, 1], [4400, 2], [3700, 3], [3200, 4], [2800, 5], [2400, 6], [2000, 7], [1800, 8], [1600, 9], [1400, 10], [1200, 11], [1100, 12], [1000, 13], [900, 14], [700, 15], [500, 16], [300, 17], [50, 18], [0, 19], [-200, 21], [-400, 23], [-600, 25], [-1200, 27], [-51500, 30]]}, {"typeId": "rankLine", "rankLine": [[7462, 1], [4400, 2], [3700, 3], [3000, 4], [2400, 5], [1900, 6], [1500, 7], [1200, 8], [1000, 9], [800, 10], [500, 11], [200, 12], [100, 13], [-100, 14], [-300, 15], [-600, 16], [-1200, 17], [-51500, 18]]}, {"typeId": "rankLine", "rankLine": [[7165, 1], [3600, 2], [2700, 3], [2100, 4], [1600, 5], [400, 6], [200, 7], [0, 8], [-200, 9], [-600, 10], [-1200, 11], [-51500, 12]]}, {"typeId": "rankLine", "rankLine": [[7500, 1], [1500, 2], [850, 3], [0, 4], [-1200, 5], [-51500, 6]]}, {"typeId": "tableRank"}], "fees": [{"assetId": "bank:qjddz:diamond", "count": 4}], "rankRewards": [{"rankRange": [1, 1], "desc": "5元微信红包", "rewards": [{"assetId": "bank:coupon", "count": 500}]}, {"rankRange": [2, 2], "desc": "3元微信红包", "rewards": [{"assetId": "bank:coupon", "count": 300}]}, {"rankRange": [3, 3], "desc": "2元微信红包", "rewards": [{"assetId": "bank:coupon", "count": 200}]}, {"rankRange": [4, 6], "desc": "1.2元微信红包", "rewards": [{"assetId": "bank:coupon", "count": 120}]}, {"rankRange": [7, 12], "desc": "0.8元微信红包", "rewards": [{"assetId": "bank:coupon", "count": 80}]}, {"rankRange": [13, 18], "desc": "0.4元微信红包", "rewards": [{"assetId": "bank:coupon", "count": 40}]}, {"rankRange": [19, 30], "desc": "3200金币", "rewards": [{"assetId": "user:gcoin", "count": 3200}]}]}