{"projectsPath": "/home/<USER>/work/tomato-py", "deployPath": "/home/<USER>/work/xyqp/deploy", "pypy": "pypy", "configRedis": {"host": "127.0.0.1", "port": 6379, "db": 0}, "configPath": "/home/<USER>/work/tomato-py/xyqp-conf/dev", "appModule": "xyqp", "projects": [{"name": "tomato", "path": "tomato-py", "sources": ["src"]}, {"name": "xybase", "path": "xybase-py", "sources": ["src"]}, {"name": "xysdka<PERSON>", "path": "xysdkapi-py", "sources": ["src"]}, {"name": "xyqp", "path": "xyqp-py", "sources": ["src"]}, {"name": "xyqpgame", "path": "xyqpgame-py", "sources": ["src"]}, {"name": "ddzbase", "path": "ddzbase-py", "sources": ["src"]}, {"name": "qjddz", "path": "qjddz-py", "sources": ["src"]}]}