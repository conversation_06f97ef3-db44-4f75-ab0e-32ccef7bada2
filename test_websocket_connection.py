#!/usr/bin/env python
# -*- coding=utf-8 -*-
'''
WebSocket连接测试脚本 - 测试直播间服务
'''

import asyncio
import websockets
import json
import time

async def test_websocket_connection():
    """测试WebSocket连接和消息处理"""
    uri = "ws://127.0.0.1:9200/"
    
    print("=" * 60)
    print("直播间WebSocket连接测试")
    print("=" * 60)
    print(f"连接地址: {uri}")
    
    try:
        print("正在连接WebSocket服务器...")
        
        # 连接WebSocket服务器
        async with websockets.connect(uri, timeout=10) as websocket:
            print("✅ WebSocket连接成功!")
            print(f"连接状态: {websocket.state}")
            print(f"本地地址: {websocket.local_address}")
            print(f"远程地址: {websocket.remote_address}")
            
            # 测试消息列表
            test_messages = [
                {
                    "route": "/user/bind",
                    "userId": "test_user_001",
                    "token": "test_token_123"
                },
                {
                    "route": "/user/bindAndEnterRoom",
                    "userId": "test_user_001",
                    "roomId": "100216",
                    "token": "test_token_123"
                }
            ]
            
            for i, message in enumerate(test_messages, 1):
                print(f"\n--- 测试消息 {i}: {message['route']} ---")
                message_str = json.dumps(message, ensure_ascii=False)
                print(f"发送: {message_str}")
                
                await websocket.send(message_str)
                print("✅ 消息发送成功")
                
                try:
                    # 等待响应
                    response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                    print(f"收到响应: {response}")
                    
                    # 尝试解析JSON响应
                    try:
                        response_data = json.loads(response)
                        print(f"解析后的响应:")
                        print(json.dumps(response_data, indent=2, ensure_ascii=False))
                    except json.JSONDecodeError:
                        print(f"响应不是JSON格式: {response}")
                        
                except asyncio.TimeoutError:
                    print("⚠️  等待响应超时")
                
                await asyncio.sleep(1)  # 等待1秒再发送下一条消息
            
            # 测试心跳消息
            print(f"\n--- 心跳测试 ---")
            heartbeat_msg = {
                "route": "/heartbeat",
                "timestamp": int(time.time())
            }
            
            message_str = json.dumps(heartbeat_msg)
            print(f"发送心跳: {message_str}")
            await websocket.send(message_str)
            
            try:
                response = await asyncio.wait_for(websocket.recv(), timeout=3.0)
                print(f"心跳响应: {response}")
            except asyncio.TimeoutError:
                print("心跳响应超时")
            
            print(f"\n--- 保持连接测试 ---")
            print("保持连接10秒，监听服务器消息...")
            
            try:
                # 监听服务器消息10秒
                for i in range(10):
                    try:
                        message = await asyncio.wait_for(websocket.recv(), timeout=1.0)
                        print(f"收到服务器消息: {message}")
                    except asyncio.TimeoutError:
                        print(f".", end="", flush=True)
                    await asyncio.sleep(1)
            except websockets.exceptions.ConnectionClosed:
                print("\n⚠️  连接被服务器关闭")
            
            print(f"\n✅ WebSocket测试完成")
            
    except websockets.exceptions.InvalidURI:
        print("❌ 无效的WebSocket URI")
    except websockets.exceptions.InvalidHandshake as e:
        print(f"❌ WebSocket握手失败: {e}")
        print("可能的原因:")
        print("1. 服务器不支持WebSocket协议")
        print("2. 服务器返回了错误的握手响应")
        print("3. 网络连接问题")
    except ConnectionRefusedError:
        print("❌ 连接被拒绝")
        print("可能的原因:")
        print("1. WebSocket服务器未启动")
        print("2. 端口9200未监听")
        print("3. 防火墙阻止连接")
        print("\n请先运行: python start_all_services_final.py")
    except asyncio.TimeoutError:
        print("❌ 连接超时")
        print("可能的原因:")
        print("1. 服务器响应太慢")
        print("2. 网络延迟过高")
        print("3. 服务器负载过高")
    except Exception as e:
        print(f"❌ 连接失败: {e}")
        print(f"错误类型: {type(e).__name__}")

def test_port_connectivity():
    """测试端口连通性"""
    import socket
    
    print("\n" + "=" * 60)
    print("端口连通性测试")
    print("=" * 60)
    
    ports = [9200, 9000]  # WebSocket和HTTP端口
    
    for port in ports:
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.settimeout(3)
                result = s.connect_ex(('127.0.0.1', port))
                
                if result == 0:
                    if port == 9200:
                        print(f"✅ 端口{port} (WebSocket服务) 可以连接")
                    elif port == 9000:
                        print(f"✅ 端口{port} (HTTP服务) 可以连接")
                else:
                    if port == 9200:
                        print(f"❌ 端口{port} (WebSocket服务) 无法连接")
                    elif port == 9000:
                        print(f"❌ 端口{port} (HTTP服务) 无法连接")
                    
        except Exception as e:
            print(f"❌ 端口{port}测试失败: {e}")

async def main():
    """主函数"""
    # 首先测试端口连通性
    test_port_connectivity()
    
    # 然后测试WebSocket连接
    await test_websocket_connection()

if __name__ == '__main__':
    try:
        # 检查是否安装了websockets库
        import websockets
        print("使用websockets库进行测试...")
        asyncio.run(main())
    except ImportError:
        print("websockets库未安装，只进行端口测试...")
        test_port_connectivity()
        print("\n如需完整WebSocket测试，请安装websockets库:")
        print("pip install websockets")
