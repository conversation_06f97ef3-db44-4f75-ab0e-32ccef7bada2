2025-06-01 22:52:30,046 - RoomServer - WARNING - 未以管理员权限运行，某些功能可能受限
2025-06-01 22:52:30,046 - RoomServer - INFO - 尝试获取管理员权限...
2025-06-01 22:52:31,837 - RoomServer - WARNING - 无法获取管理员权限，继续以普通权限运行
2025-06-01 22:52:31,838 - RoomServer - INFO - 启动房间服务器...
2025-06-01 22:52:31,839 - RoomServer - INFO - Redis连接正常
2025-06-01 22:52:31,839 - RoomServer - WARNING - 未以管理员权限运行，无法关闭防火墙
2025-06-01 22:52:32,108 - RoomServer - INFO - 启动房间服务器...
2025-06-01 22:52:32,155 - RoomServer - INFO - Redis连接正常
2025-06-01 22:52:32,156 - RoomServer - INFO - 临时关闭Windows防火墙...
2025-06-01 22:52:32,296 - RoomServer - INFO - Windows防火墙已临时关闭
2025-06-01 22:52:33,858 - RoomServer - INFO - 导入server模块...
2025-06-01 22:52:34,312 - RoomServer - INFO - 导入server模块...
2025-06-01 22:52:34,845 - RoomServer - INFO - 成功导入server模块
2025-06-01 22:52:34,845 - RoomServer - INFO - 配置文件路径: I:\live_room_service-master\live_room_service-master\deploy\config
2025-06-01 22:52:34,845 - RoomServer - WARNING - 配置文件不存在: I:\live_room_service-master\live_room_service-master\deploy\config\RM000001.json
2025-06-01 22:52:34,845 - RoomServer - INFO - 启动服务: RM000001
2025-06-01 22:52:34,910 - tomato - INFO - I 2239420969168 server.runWithFileConf serverId= RM000001 app= <fqparty.application.Application object at 0x000002094F1C27F0> appArgs= []
2025-06-01 22:52:34,910 - RoomServer - INFO - 服务已成功启动
2025-06-01 22:52:34,996 - RoomServer - INFO - 成功导入server模块
2025-06-01 22:52:34,996 - RoomServer - INFO - 配置文件路径: I:\live_room_service-master\live_room_service-master\deploy\config
2025-06-01 22:52:34,997 - RoomServer - WARNING - 配置文件不存在: I:\live_room_service-master\live_room_service-master\deploy\config\RM000001.json
2025-06-01 22:52:34,998 - RoomServer - INFO - 启动服务: RM000001
2025-06-01 22:52:35,019 - tomato - INFO - I 2313652922976 server.runWithFileConf serverId= RM000001 app= <fqparty.application.Application object at 0x0000021AB046E880> appArgs= []
2025-06-01 22:52:35,020 - RoomServer - INFO - 服务已成功启动
2025-06-01 22:54:13,939 - RoomServer - WARNING - 未以管理员权限运行，某些功能可能受限
2025-06-01 22:54:13,939 - RoomServer - INFO - 尝试获取管理员权限...
2025-06-01 22:54:15,775 - RoomServer - WARNING - 无法获取管理员权限，继续以普通权限运行
2025-06-01 22:54:15,775 - RoomServer - INFO - 启动房间服务器...
2025-06-01 22:54:15,775 - RoomServer - INFO - Redis连接正常
2025-06-01 22:54:15,777 - RoomServer - WARNING - 未以管理员权限运行，无法关闭防火墙
2025-06-01 22:54:16,032 - RoomServer - INFO - 启动房间服务器...
2025-06-01 22:54:16,050 - RoomServer - INFO - Redis连接正常
2025-06-01 22:54:16,050 - RoomServer - INFO - 临时关闭Windows防火墙...
2025-06-01 22:54:16,186 - RoomServer - INFO - Windows防火墙已临时关闭
2025-06-01 22:54:17,804 - RoomServer - INFO - 导入server模块...
2025-06-01 22:54:18,215 - RoomServer - INFO - 导入server模块...
2025-06-01 22:54:18,779 - RoomServer - INFO - 成功导入server模块
2025-06-01 22:54:18,779 - RoomServer - INFO - 配置文件路径: I:\live_room_service-master\live_room_service-master\deploy\config
2025-06-01 22:54:18,779 - RoomServer - INFO - 服务配置: {
  "server_id": "RM000001",
  "server_name": "房间服务器",
  "server_type": "room",
  "host": "0.0.0.0",
  "port": 9200,
  "ws_path": "/",
  "redis": {
    "host": "127.0.0.1",
    "port": 6379,
    "db": 0
  },
  "log_level": "DEBUG",
  "max_connections": 1000,
  "heartbeat_interval": 30,
  "timeout": 60,
  "room_config": {
    "max_users_per_room": 100,
    "default_room": "lobby"
  }
}
2025-06-01 22:54:18,780 - RoomServer - INFO - 启动服务: RM000001
2025-06-01 22:54:18,805 - tomato - INFO - I 2765113340112 server.runWithFileConf serverId= RM000001 app= <fqparty.application.Application object at 0x00000283D3808BB0> appArgs= []
2025-06-01 22:54:18,806 - RoomServer - INFO - 服务已成功启动
2025-06-01 22:54:18,960 - RoomServer - INFO - 成功导入server模块
2025-06-01 22:54:18,960 - RoomServer - INFO - 配置文件路径: I:\live_room_service-master\live_room_service-master\deploy\config
2025-06-01 22:54:18,961 - RoomServer - INFO - 服务配置: {
  "server_id": "RM000001",
  "server_name": "房间服务器",
  "server_type": "room",
  "host": "0.0.0.0",
  "port": 9200,
  "ws_path": "/",
  "redis": {
    "host": "127.0.0.1",
    "port": 6379,
    "db": 0
  },
  "log_level": "DEBUG",
  "max_connections": 1000,
  "heartbeat_interval": 30,
  "timeout": 60,
  "room_config": {
    "max_users_per_room": 100,
    "default_room": "lobby"
  }
}
2025-06-01 22:54:18,961 - RoomServer - INFO - 启动服务: RM000001
2025-06-01 22:54:18,985 - tomato - INFO - I 2380758116960 server.runWithFileConf serverId= RM000001 app= <fqparty.application.Application object at 0x0000022A526A7580> appArgs= []
2025-06-01 22:54:18,986 - RoomServer - INFO - 服务已成功启动
2025-06-01 22:56:56,094 - RoomServer - WARNING - 未以管理员权限运行，某些功能可能受限
2025-06-01 22:56:56,095 - RoomServer - INFO - 尝试获取管理员权限...
2025-06-01 22:56:57,712 - RoomServer - WARNING - 无法获取管理员权限，继续以普通权限运行
2025-06-01 22:56:57,712 - RoomServer - INFO - 启动房间服务器...
2025-06-01 22:56:57,713 - RoomServer - INFO - Redis连接正常
2025-06-01 22:56:57,713 - RoomServer - WARNING - 未以管理员权限运行，无法关闭防火墙
2025-06-01 22:56:57,976 - RoomServer - INFO - 启动房间服务器...
2025-06-01 22:56:57,977 - RoomServer - INFO - Redis连接正常
2025-06-01 22:56:57,978 - RoomServer - INFO - 临时关闭Windows防火墙...
2025-06-01 22:56:58,126 - RoomServer - INFO - Windows防火墙已临时关闭
2025-06-01 22:56:59,748 - RoomServer - INFO - 配置文件已存在: I:\live_room_service-master\live_room_service-master\deploy\config\RM000001.json
2025-06-01 22:56:59,749 - RoomServer - INFO - 导入server模块...
2025-06-01 22:57:00,156 - RoomServer - INFO - 配置文件已存在: I:\live_room_service-master\live_room_service-master\deploy\config\RM000001.json
2025-06-01 22:57:00,161 - RoomServer - INFO - 导入server模块...
2025-06-01 22:57:00,714 - RoomServer - INFO - 成功导入server模块
2025-06-01 22:57:00,715 - RoomServer - INFO - 配置文件路径: I:\live_room_service-master\live_room_service-master\deploy\config
2025-06-01 22:57:00,715 - RoomServer - INFO - 服务配置: {'server_id': 'RM000001', 'server_name': '房间服务器', 'server_type': 'room', 'host': '0.0.0.0', 'port': 9200, 'ws_path': '/', 'redis': {'host': '127.0.0.1', 'port': 6379, 'db': 0}, 'log_level': 'DEBUG', 'max_connections': 1000, 'heartbeat_interval': 30, 'timeout': 60, 'room_config': {'max_users_per_room': 100, 'default_room': 'lobby'}}
2025-06-01 22:57:00,737 - tomato - INFO - I 2119342530176 server.runWithFileConf serverId= RM000001 app= <fqparty.application.Application object at 0x000001ED76B82100> appArgs= []
2025-06-01 22:57:00,737 - RoomServer - INFO - 等待服务器初始化...
2025-06-01 22:57:00,893 - RoomServer - INFO - 成功导入server模块
2025-06-01 22:57:00,894 - RoomServer - INFO - 配置文件路径: I:\live_room_service-master\live_room_service-master\deploy\config
2025-06-01 22:57:00,895 - RoomServer - INFO - 服务配置: {'server_id': 'RM000001', 'server_name': '房间服务器', 'server_type': 'room', 'host': '0.0.0.0', 'port': 9200, 'ws_path': '/', 'redis': {'host': '127.0.0.1', 'port': 6379, 'db': 0}, 'log_level': 'DEBUG', 'max_connections': 1000, 'heartbeat_interval': 30, 'timeout': 60, 'room_config': {'max_users_per_room': 100, 'default_room': 'lobby'}}
2025-06-01 22:57:00,946 - tomato - INFO - I 1911389434208 server.runWithFileConf serverId= RM000001 app= <fqparty.application.Application object at 0x000001BD0780F7C0> appArgs= []
2025-06-01 22:57:00,947 - RoomServer - INFO - 等待服务器初始化...
2025-06-01 22:57:04,775 - RoomServer - ERROR - 服务器未能在端口 9200 上启动
2025-06-01 22:57:04,775 - RoomServer - ERROR - 服务启动失败
2025-06-01 22:57:04,775 - RoomServer - INFO - 服务已停止
2025-06-01 22:57:04,966 - RoomServer - ERROR - 服务器未能在端口 9200 上启动
2025-06-01 22:57:04,966 - RoomServer - ERROR - 服务启动失败
2025-06-01 22:57:04,967 - RoomServer - INFO - 服务已停止
2025-06-01 22:57:15,222 - RoomServer - INFO - 收到信号: 2
