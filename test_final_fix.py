#!/usr/bin/env python
# -*- coding=utf-8 -*-
'''
最终修复版本 - 测试WebSocket消息接收
'''

import asyncio
import websockets
import json

async def test_final_fix():
    """最终修复版本测试"""
    uri = "ws://127.0.0.1:9200/"
    
    print("=" * 60)
    print("最终修复版本 - WebSocket消息测试")
    print("=" * 60)
    print(f"连接地址: {uri}")
    print("协议: wsold (TTComposerFake)")
    
    try:
        async with websockets.connect(uri, timeout=10) as websocket:
            print("\n✅ WebSocket连接成功!")
            print(f"连接状态: {websocket.state}")
            
            # 发送最简单的用户绑定消息
            print(f"\n--- 发送用户绑定消息 ---")
            message = {
                "route": "/user/bind",
                "userId": "100216",
                "token": "072d576456d38bfc41208c404fc3c087"
            }
            
            message_str = json.dumps(message)
            print(f"发送: {message_str}")
            
            await websocket.send(message_str)
            print("✅ 消息发送成功")
            
            # 等待响应
            print("等待服务器响应...")
            try:
                response = await asyncio.wait_for(websocket.recv(), timeout=15.0)
                print(f"🎉 收到响应: {response}")
                
                try:
                    response_data = json.loads(response)
                    print("✅ 解析后的响应:")
                    print(json.dumps(response_data, indent=2, ensure_ascii=False))
                    
                    if response_data.get("ec") == 0:
                        print("🎉 用户绑定成功!")
                    else:
                        print(f"⚠️  绑定失败: {response_data.get('info', '未知错误')}")
                        
                except json.JSONDecodeError:
                    print(f"响应不是JSON格式: {response}")
                    
            except asyncio.TimeoutError:
                print("⚠️  响应超时 - 但连接正常")
            
            # 如果第一个消息没有响应，尝试进入房间
            print(f"\n--- 发送进入房间消息 ---")
            room_message = {
                "route": "/user/bindAndEnterRoom",
                "roomId": 100216,
                "authToken": "072d576456d38bfc41208c404fc3c087",
                "platform": "iOS,18.3",
                "version": "5.0.0",
                "isReceivePush": 1,
                "onMicForLogout": 1
            }
            
            message_str = json.dumps(room_message)
            print(f"发送: {message_str}")
            
            await websocket.send(message_str)
            print("✅ 消息发送成功")
            
            # 等待响应
            print("等待服务器响应...")
            try:
                response = await asyncio.wait_for(websocket.recv(), timeout=15.0)
                print(f"🎉 收到响应: {response}")
                
                try:
                    response_data = json.loads(response)
                    print("✅ 解析后的响应:")
                    print(json.dumps(response_data, indent=2, ensure_ascii=False))
                    
                    if response_data.get("ec") == 0:
                        print("🎉 进入房间成功!")
                        room_info = response_data.get("result", {})
                        if room_info:
                            print(f"房间信息: {room_info}")
                    else:
                        print(f"⚠️  进入房间失败: {response_data.get('info', '未知错误')}")
                        
                except json.JSONDecodeError:
                    print(f"响应不是JSON格式: {response}")
                    
            except asyncio.TimeoutError:
                print("⚠️  响应超时 - 但连接正常")
            
            # 保持连接，监听推送消息
            print(f"\n--- 监听服务器推送 ---")
            print("保持连接20秒，监听服务器推送消息...")
            
            try:
                for i in range(20):
                    try:
                        message = await asyncio.wait_for(websocket.recv(), timeout=1.0)
                        print(f"\n📨 收到服务器推送: {message}")
                        
                        try:
                            msg_data = json.loads(message)
                            print("解析后的推送消息:")
                            print(json.dumps(msg_data, indent=2, ensure_ascii=False))
                        except json.JSONDecodeError:
                            print(f"推送消息不是JSON格式: {message}")
                            
                    except asyncio.TimeoutError:
                        print(".", end="", flush=True)
                    await asyncio.sleep(1)
                    
            except websockets.exceptions.ConnectionClosed:
                print("\n⚠️  连接被服务器关闭")
            
            print(f"\n\n✅ 测试完成")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

async def main():
    """主函数"""
    await test_final_fix()

if __name__ == '__main__':
    try:
        import websockets
        print("开始最终修复版本测试...")
        asyncio.run(main())
    except ImportError:
        print("websockets库未安装")
        print("pip install websockets")
