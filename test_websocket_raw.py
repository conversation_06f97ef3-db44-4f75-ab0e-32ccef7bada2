#!/usr/bin/env python
# -*- coding=utf-8 -*-
'''
原始WebSocket握手测试
'''

import socket
import base64
import hashlib

def test_websocket_handshake():
    """测试WebSocket握手"""
    print("=" * 60)
    print("WebSocket握手测试")
    print("=" * 60)
    
    try:
        # 创建socket连接
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(10)
        
        print("连接到 127.0.0.1:9200...")
        sock.connect(('127.0.0.1', 9200))
        print("✅ TCP连接成功")
        
        # 生成WebSocket密钥
        key = base64.b64encode(b"test_websocket_key_123").decode()
        
        # 构造WebSocket握手请求
        handshake_request = (
            f"GET / HTTP/1.1\r\n"
            f"Host: 127.0.0.1:9200\r\n"
            f"Upgrade: websocket\r\n"
            f"Connection: Upgrade\r\n"
            f"Sec-WebSocket-Key: {key}\r\n"
            f"Sec-WebSocket-Version: 13\r\n"
            f"Origin: http://127.0.0.1\r\n"
            f"\r\n"
        )
        
        print("发送WebSocket握手请求:")
        print("-" * 40)
        print(handshake_request)
        print("-" * 40)
        
        # 发送握手请求
        sock.send(handshake_request.encode())
        
        # 接收响应
        print("等待服务器响应...")
        response = sock.recv(4096).decode()
        
        print("服务器响应:")
        print("-" * 40)
        print(response)
        print("-" * 40)
        
        # 分析响应
        if "HTTP/1.1 101" in response:
            print("✅ WebSocket握手成功!")
            
            # 验证Sec-WebSocket-Accept
            if "Sec-WebSocket-Accept:" in response:
                print("✅ 包含Sec-WebSocket-Accept头")
                
                # 计算期望的accept值
                magic_string = "258EAFA5-E914-47DA-95CA-C5AB0DC85B11"
                expected_accept = base64.b64encode(
                    hashlib.sha1((key + magic_string).encode()).digest()
                ).decode()
                
                if expected_accept in response:
                    print("✅ Sec-WebSocket-Accept值正确")
                else:
                    print("⚠️  Sec-WebSocket-Accept值不正确")
                    print(f"期望: {expected_accept}")
            
            # 尝试发送WebSocket数据帧
            print("\n测试发送WebSocket数据帧...")
            
            # 构造一个简单的文本帧
            message = '{"action":"ping","data":"test"}'
            message_bytes = message.encode()
            
            # WebSocket帧格式: FIN(1) + RSV(3) + Opcode(4) + MASK(1) + Payload length(7) + Masking key(32) + Payload
            frame = bytearray()
            frame.append(0x81)  # FIN=1, Opcode=1 (text frame)
            
            payload_len = len(message_bytes)
            if payload_len < 126:
                frame.append(0x80 | payload_len)  # MASK=1, payload length
            else:
                frame.append(0x80 | 126)
                frame.extend(payload_len.to_bytes(2, 'big'))
            
            # 添加掩码
            mask = b'\x12\x34\x56\x78'
            frame.extend(mask)
            
            # 添加掩码后的载荷
            for i, byte in enumerate(message_bytes):
                frame.append(byte ^ mask[i % 4])
            
            sock.send(frame)
            print(f"发送消息: {message}")
            
            # 尝试接收响应
            try:
                sock.settimeout(5)
                response_frame = sock.recv(1024)
                if response_frame:
                    print(f"收到响应帧: {response_frame.hex()}")
                    # 简单解析响应帧
                    if len(response_frame) >= 2:
                        opcode = response_frame[0] & 0x0F
                        payload_len = response_frame[1] & 0x7F
                        print(f"响应帧类型: {opcode}, 载荷长度: {payload_len}")
                else:
                    print("没有收到响应帧")
            except socket.timeout:
                print("等待响应帧超时")
                
        elif "HTTP" in response:
            print("❌ 服务器拒绝WebSocket升级")
            if "400" in response:
                print("错误: 400 Bad Request")
            elif "404" in response:
                print("错误: 404 Not Found")
            elif "405" in response:
                print("错误: 405 Method Not Allowed")
        else:
            print("❌ 服务器响应格式未知")
            
        sock.close()
        
    except socket.timeout:
        print("❌ 连接超时")
    except ConnectionRefusedError:
        print("❌ 连接被拒绝")
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_simple_http():
    """测试简单HTTP请求"""
    print("\n" + "=" * 60)
    print("简单HTTP请求测试")
    print("=" * 60)
    
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5)
        
        print("连接到 127.0.0.1:9200...")
        sock.connect(('127.0.0.1', 9200))
        
        # 发送简单HTTP请求
        http_request = (
            "GET / HTTP/1.1\r\n"
            "Host: 127.0.0.1:9200\r\n"
            "Connection: close\r\n"
            "\r\n"
        )
        
        print("发送HTTP请求:")
        print(http_request)
        
        sock.send(http_request.encode())
        
        # 接收响应
        response = sock.recv(4096).decode()
        
        print("服务器响应:")
        print("-" * 40)
        print(response)
        print("-" * 40)
        
        sock.close()
        
    except Exception as e:
        print(f"HTTP测试失败: {e}")

if __name__ == '__main__':
    test_websocket_handshake()
    test_simple_http()
