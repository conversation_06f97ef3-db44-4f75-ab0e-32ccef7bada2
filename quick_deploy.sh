#!/bin/bash

# live_room_service 快速部署脚本
# 适用于宝塔面板环境

echo "=========================================="
echo "live_room_service 快速部署脚本"
echo "=========================================="

# 设置项目路径
PROJECT_PATH="/www/wwwroot/live_room_service"

# 检查是否为root用户
if [ "$EUID" -ne 0 ]; then
    echo "❌ 请使用root用户运行此脚本"
    exit 1
fi

# 步骤1: 检查系统环境
echo "🔍 步骤1: 检查系统环境..."

# 检查Python3
if ! command -v python3 &> /dev/null; then
    echo "❌ Python3 未安装，请先安装Python3"
    exit 1
fi
echo "✅ Python3: $(python3 --version)"

# 检查pip3
if ! command -v pip3 &> /dev/null; then
    echo "❌ pip3 未安装，请先安装pip3"
    exit 1
fi
echo "✅ pip3: $(pip3 --version)"

# 步骤2: 安装系统依赖
echo "🔍 步骤2: 安装系统依赖..."

# 检查并安装Redis
if ! command -v redis-server &> /dev/null; then
    echo "📦 安装Redis..."
    if command -v yum &> /dev/null; then
        yum install -y redis
    elif command -v apt &> /dev/null; then
        apt update && apt install -y redis-server
    else
        echo "❌ 无法自动安装Redis，请手动安装"
        exit 1
    fi
fi

# 启动Redis服务
systemctl start redis
systemctl enable redis
echo "✅ Redis服务已启动"

# 检查并安装Supervisor
if ! command -v supervisorctl &> /dev/null; then
    echo "📦 安装Supervisor..."
    pip3 install supervisor
fi
echo "✅ Supervisor已安装"

# 步骤3: 创建项目目录
echo "🔍 步骤3: 创建项目目录..."
mkdir -p $PROJECT_PATH
mkdir -p $PROJECT_PATH/logs
echo "✅ 项目目录已创建: $PROJECT_PATH"

# 步骤4: 安装Python依赖
echo "🔍 步骤4: 安装Python依赖..."
cd $PROJECT_PATH

# 创建虚拟环境 (可选)
read -p "是否创建Python虚拟环境? (y/n): " create_venv
if [ "$create_venv" = "y" ] || [ "$create_venv" = "Y" ]; then
    echo "📦 创建虚拟环境..."
    python3 -m venv venv
    source venv/bin/activate
    echo "✅ 虚拟环境已创建并激活"
fi

# 安装依赖
if [ -f "requirements.txt" ]; then
    echo "📦 安装项目依赖..."
    pip3 install -r requirements.txt
else
    echo "📦 安装核心依赖..."
    pip3 install twisted redis stackless
fi
echo "✅ Python依赖安装完成"

# 步骤5: 设置启动脚本权限
echo "🔍 步骤5: 设置启动脚本权限..."
chmod +x $PROJECT_PATH/start_bt.sh
chmod +x $PROJECT_PATH/start_production.py
echo "✅ 启动脚本权限已设置"

# 步骤6: 配置Supervisor
echo "🔍 步骤6: 配置Supervisor..."

# 创建Supervisor配置目录
mkdir -p /etc/supervisor/conf.d

# 复制Supervisor配置文件
if [ -f "$PROJECT_PATH/supervisor_config.conf" ]; then
    cp $PROJECT_PATH/supervisor_config.conf /etc/supervisor/conf.d/live_room_service.conf
    echo "✅ Supervisor配置文件已复制"
else
    echo "⚠️  Supervisor配置文件不存在，请手动配置"
fi

# 重新加载Supervisor配置
if command -v supervisorctl &> /dev/null; then
    supervisorctl reread
    supervisorctl update
    echo "✅ Supervisor配置已重新加载"
fi

# 步骤7: 配置防火墙
echo "🔍 步骤7: 配置防火墙..."

# 开放端口9200
if command -v firewall-cmd &> /dev/null; then
    firewall-cmd --permanent --add-port=9200/tcp
    firewall-cmd --reload
    echo "✅ 防火墙已配置 (firewall-cmd)"
elif command -v ufw &> /dev/null; then
    ufw allow 9200/tcp
    echo "✅ 防火墙已配置 (ufw)"
else
    echo "⚠️  请手动配置防火墙，开放端口9200"
fi

# 步骤8: 启动服务
echo "🔍 步骤8: 启动服务..."

# 测试Redis连接
if redis-cli ping > /dev/null 2>&1; then
    echo "✅ Redis连接测试成功"
else
    echo "❌ Redis连接测试失败"
    exit 1
fi

# 启动服务
if command -v supervisorctl &> /dev/null; then
    supervisorctl start live_room_service
    sleep 3
    
    # 检查服务状态
    if supervisorctl status live_room_service | grep RUNNING > /dev/null; then
        echo "✅ 服务启动成功"
    else
        echo "❌ 服务启动失败，请检查日志"
        supervisorctl status live_room_service
    fi
else
    echo "⚠️  Supervisor未配置，请手动启动服务"
fi

# 步骤9: 验证部署
echo "🔍 步骤9: 验证部署..."

# 检查端口监听
if netstat -tlnp | grep :9200 > /dev/null 2>&1; then
    echo "✅ 端口9200正在监听"
else
    echo "❌ 端口9200未监听，请检查服务状态"
fi

# 部署完成
echo "=========================================="
echo "🎉 部署完成!"
echo "=========================================="
echo "服务信息:"
echo "  - WebSocket地址: ws://$(hostname -I | awk '{print $1}'):9200/"
echo "  - 项目路径: $PROJECT_PATH"
echo "  - 日志路径: $PROJECT_PATH/logs/"
echo ""
echo "管理命令:"
echo "  - 启动服务: supervisorctl start live_room_service"
echo "  - 停止服务: supervisorctl stop live_room_service"
echo "  - 重启服务: supervisorctl restart live_room_service"
echo "  - 查看状态: supervisorctl status live_room_service"
echo "  - 查看日志: tail -f $PROJECT_PATH/logs/supervisor.log"
echo ""
echo "下一步:"
echo "  1. 在宝塔面板中配置域名和SSL (可选)"
echo "  2. 配置反向代理 (可选)"
echo "  3. 使用客户端连接测试服务"
echo "=========================================="
