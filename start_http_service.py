#!/usr/bin/env python
# -*- coding=utf-8 -*-
'''
直接在当前终端启动HTTP服务
'''

import os
import sys
import traceback

def main():
    print("=" * 60)
    print("启动HTTP服务器 (HT000001)")
    print("=" * 60)
    
    # 项目路径配置
    current_dir = os.path.dirname(os.path.abspath(__file__))
    bin_dir = os.path.join(current_dir, 'deploy', 'bin')
    config_path = os.path.join(current_dir, 'deploy', 'config')
    logs_path = os.path.join(current_dir, 'deploy', 'logs')
    
    # 确保日志目录存在
    os.makedirs(logs_path, exist_ok=True)
    
    # 设置路径
    sys.path.insert(0, bin_dir)
    
    try:
        print("1. 导入模块...")
        import stackless
        import tomato
        from tomato.config import configure
        from tomato.utils import ttlog
        import fqparty
        print("   [OK] 模块导入成功")
        
        print("2. 初始化配置...")
        configure.initByFile(config_path)
        print("   [OK] 配置初始化成功")
        
        print("3. 设置日志级别...")
        logLevel = configure.loadJson('server.tomato.global', {}).get('log', {}).get('level', ttlog.INFO)
        ttlog.setLevel(logLevel)
        print(f"   [OK] 日志级别: {logLevel}")
        
        print("4. 初始化tomato应用...")
        tomato.app.appArgs = []
        tomato.app.init('HT000001')
        print("   [OK] tomato应用初始化成功")
        
        print("5. 初始化fqparty应用...")
        fqparty.app.init()
        print("   [OK] fqparty应用初始化成功")
        
        print("6. 启动fqparty应用...")
        fqparty.app.start()
        print("   [OK] fqparty应用启动成功")
        
        print("7. 启动tomato应用...")
        tomato.app.start()
        print("   [OK] tomato应用启动成功")
        
        print("\n" + "=" * 60)
        print("HTTP服务器启动成功!")
        print("=" * 60)
        print("访问地址: http://127.0.0.1:9000/")
        print("按 Ctrl+C 停止服务")
        print("=" * 60)
        
        # 保持服务运行
        try:
            while True:
                import time
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n\n服务已停止")
        
    except Exception as e:
        print(f"\n[ERROR] 启动失败: {e}")
        print("\n详细错误信息:")
        print("-" * 50)
        traceback.print_exc()
        print("-" * 50)
        
        input("\n按回车键退出...")

if __name__ == '__main__':
    main()
