#!/usr/bin/env python
# -*- coding=utf-8 -*-
'''
测试房间100216的WebSocket连接
'''

import asyncio
import websockets
import json
import time

async def test_room_connection():
    """测试连接到房间100216"""
    uri = "ws://12*******:9200/"
    
    print("=" * 60)
    print("测试房间100216的WebSocket连接")
    print("=" * 60)
    print(f"连接地址: {uri}")
    print(f"目标房间: 100216")
    
    try:
        print("\n正在连接WebSocket服务器...")
        
        # 连接WebSocket服务器
        async with websockets.connect(uri, timeout=10) as websocket:
            print("✅ WebSocket连接成功!")
            print(f"连接状态: {websocket.state}")
            print(f"本地地址: {websocket.local_address}")
            print(f"远程地址: {websocket.remote_address}")
            
            # 步骤1: 用户绑定
            print(f"\n--- 步骤1: 用户绑定 ---")
            bind_message = {
                "msgType": 1,  # TYPE_REQUEST
                "msgId": 1001,
                "route": "/user/bind",
                "body": {
                    "userId": "100216",
                    "token": "token_100216_test"
                }
            }
            
            message_str = json.dumps(bind_message, ensure_ascii=False)
            print(f"发送用户绑定: {message_str}")
            
            await websocket.send(message_str)
            print("✅ 用户绑定消息发送成功")
            
            try:
                response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                print(f"用户绑定响应: {response}")
                
                try:
                    response_data = json.loads(response)
                    print("解析后的绑定响应:")
                    print(json.dumps(response_data, indent=2, ensure_ascii=False))
                except json.JSONDecodeError:
                    print(f"绑定响应不是JSON格式: {response}")
                    
            except asyncio.TimeoutError:
                print("⚠️  用户绑定响应超时")
            
            await asyncio.sleep(2)  # 等待2秒
            
            # 步骤2: 绑定并进入房间100216
            print(f"\n--- 步骤2: 绑定并进入房间100216 ---")
            enter_room_message = {
                "msgType": 1,  # TYPE_REQUEST
                "msgId": 1002,
                "route": "/user/bindAndEnterRoom",
                "body": {
                    "roomId": "100216",
                    "authToken": "token_100216_test",  # 注意这里是authToken
                    "platform": "web",
                    "version": "1.0.0"
                }
            }
            
            message_str = json.dumps(enter_room_message, ensure_ascii=False)
            print(f"发送进入房间: {message_str}")
            
            await websocket.send(message_str)
            print("✅ 进入房间消息发送成功")
            
            try:
                response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                print(f"进入房间响应: {response}")
                
                try:
                    response_data = json.loads(response)
                    print("解析后的进入房间响应:")
                    print(json.dumps(response_data, indent=2, ensure_ascii=False))
                except json.JSONDecodeError:
                    print(f"进入房间响应不是JSON格式: {response}")
                    
            except asyncio.TimeoutError:
                print("⚠️  进入房间响应超时")
            
            await asyncio.sleep(2)  # 等待2秒
            
            # 步骤3: 发送心跳保持连接
            print(f"\n--- 步骤3: 心跳测试 ---")
            heartbeat_message = {
                "msgType": 1,  # TYPE_REQUEST
                "msgId": 1003,
                "route": "/user/heartbeat",
                "body": {
                    "timestamp": int(time.time())
                }
            }
            
            message_str = json.dumps(heartbeat_message, ensure_ascii=False)
            print(f"发送心跳: {message_str}")
            
            await websocket.send(message_str)
            
            try:
                response = await asyncio.wait_for(websocket.recv(), timeout=3.0)
                print(f"心跳响应: {response}")
            except asyncio.TimeoutError:
                print("⚠️  心跳响应超时")
            
            # 步骤4: 监听房间消息
            print(f"\n--- 步骤4: 监听房间100216消息 ---")
            print("监听15秒，等待房间消息...")
            
            try:
                for i in range(15):
                    try:
                        message = await asyncio.wait_for(websocket.recv(), timeout=1.0)
                        print(f"\n📨 收到房间消息: {message}")
                        
                        # 尝试解析消息
                        try:
                            msg_data = json.loads(message)
                            print("解析后的消息:")
                            print(json.dumps(msg_data, indent=2, ensure_ascii=False))
                        except json.JSONDecodeError:
                            print(f"消息不是JSON格式: {message}")
                            
                    except asyncio.TimeoutError:
                        print(f".", end="", flush=True)
                    await asyncio.sleep(1)
                    
            except websockets.exceptions.ConnectionClosed:
                print("\n⚠️  连接被服务器关闭")
            
            print(f"\n\n✅ 房间100216连接测试完成")
            
    except websockets.exceptions.InvalidURI:
        print("❌ 无效的WebSocket URI")
    except websockets.exceptions.InvalidHandshake as e:
        print(f"❌ WebSocket握手失败: {e}")
    except ConnectionRefusedError:
        print("❌ 连接被拒绝")
        print("请确保WebSocket服务正在运行:")
        print("python start_all_services_final.py")
    except asyncio.TimeoutError:
        print("❌ 连接超时")
    except Exception as e:
        print(f"❌ 连接失败: {e}")
        print(f"错误类型: {type(e).__name__}")

def check_services():
    """检查服务状态"""
    import socket
    
    print("=" * 60)
    print("检查服务状态")
    print("=" * 60)
    
    services = [
        (9200, "WebSocket连接服务"),
        (9000, "HTTP服务")
    ]
    
    for port, service_name in services:
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.settimeout(3)
                result = s.connect_ex(('12*******', port))
                
                if result == 0:
                    print(f"✅ {service_name} (端口{port}) 正在运行")
                else:
                    print(f"❌ {service_name} (端口{port}) 未运行")
                    
        except Exception as e:
            print(f"❌ {service_name} (端口{port}) 检查失败: {e}")

async def main():
    """主函数"""
    # 首先检查服务状态
    check_services()
    
    # 然后测试房间连接
    await test_room_connection()

if __name__ == '__main__':
    try:
        import websockets
        print("开始测试房间100216的WebSocket连接...")
        asyncio.run(main())
    except ImportError:
        print("websockets库未安装，只进行服务状态检查...")
        check_services()
        print("\n如需完整WebSocket测试，请安装websockets库:")
        print("pip install websockets")
