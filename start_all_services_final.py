#!/usr/bin/env python
# -*- coding=utf-8 -*-
'''
启动所有直播间服务的最终脚本
'''

import os
import sys
import subprocess
import time

def start_service(service_id, service_name, port=None):
    """启动单个服务"""
    print(f"启动 {service_name} ({service_id})...")
    
    # 项目路径配置
    current_dir = os.path.dirname(os.path.abspath(__file__))
    
    # 设置环境变量
    env = dict(os.environ)
    env["PYTHONPATH"] = f"{current_dir};{os.path.join(current_dir, 'deploy', 'bin')}"
    
    # 使用修复后的启动脚本
    if service_id == 'CO000001':
        cmd = [sys.executable, 'start_websocket_service.py']
    else:
        cmd = [sys.executable, 'start_single_service.py', service_id]
    
    try:
        # 在新窗口启动服务
        process = subprocess.Popen(
            cmd,
            env=env,
            cwd=current_dir,
            creationflags=subprocess.CREATE_NEW_CONSOLE
        )
        
        print(f"  [OK] {service_name} 启动成功 (PID: {process.pid})")
        return process
        
    except Exception as e:
        print(f"  [ERROR] {service_name} 启动失败: {e}")
        return None

def check_port(port):
    """检查端口是否在监听"""
    import socket
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.settimeout(2)
            result = s.connect_ex(('127.0.0.1', port))
            return result == 0
    except:
        return False

def main():
    print("=" * 60)
    print("启动所有直播间服务")
    print("=" * 60)
    
    # 服务列表
    services = [
        ('CO000001', 'WebSocket连接服务', 9200),
        ('HT000001', 'HTTP服务器', 9000),
        ('US000001', '用户服务器', None),
        ('RM000001', '房间服务器', None)
    ]
    
    processes = []
    
    print("开始启动服务...")
    print("-" * 40)
    
    for service_id, service_name, port in services:
        process = start_service(service_id, service_name, port)
        if process:
            processes.append((service_id, service_name, process, port))
        time.sleep(3)  # 给每个服务启动时间
    
    if processes:
        print("\n" + "=" * 60)
        print("等待服务完全启动...")
        print("=" * 60)
        
        # 等待服务启动
        time.sleep(15)
        
        print("\n检查服务状态:")
        print("-" * 40)
        
        running_count = 0
        for service_id, service_name, process, port in processes:
            if process.poll() is None:
                print(f"  [OK] {service_name} 运行中 (PID: {process.pid})")
                if port and check_port(port):
                    if port == 9200:
                        print(f"       WebSocket服务: ws://127.0.0.1:{port}/")
                    elif port == 9000:
                        print(f"       HTTP服务: http://127.0.0.1:{port}/")
                    else:
                        print(f"       端口 {port} 正在监听")
                elif port:
                    print(f"       警告: 端口 {port} 未监听")
                running_count += 1
            else:
                print(f"  [ERROR] {service_name} 已停止")
        
        print("\n" + "=" * 60)
        if running_count > 0:
            print(f"成功启动 {running_count}/{len(processes)} 个服务")
            print("=" * 60)
            print("\n直播间服务启动完成!")
            print("\nWebSocket连接信息:")
            print("- 连接地址: ws://127.0.0.1:9200/")
            print("- 支持的消息路由:")
            print("  * /user/bind - 用户绑定")
            print("  * /user/bindAndEnterRoom - 用户绑定并进入房间")
            print("\nHTTP API:")
            print("- API地址: http://127.0.0.1:9000/")
            print("\n日志目录: deploy/logs/")
            print("\n提示:")
            print("- 每个服务都在独立的控制台窗口中运行")
            print("- 关闭对应的控制台窗口可以停止相应的服务")
            print("- 使用 test_websocket_connection.py 测试WebSocket连接")
        else:
            print("没有服务成功启动")
            print("请检查日志文件获取详细错误信息")
        print("=" * 60)
    else:
        print("\n[ERROR] 没有服务成功启动")
    
    input("\n按回车键退出...")

if __name__ == '__main__':
    main()
