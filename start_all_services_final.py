#!/usr/bin/env python
# -*- coding=utf-8 -*-
'''
最终版本的服务启动脚本 - 在当前终端启动所有服务
'''

import os
import sys
import subprocess
import time
import threading
import signal

def main():
    print("=" * 60)
    print("🚀 启动所有直播间服务")
    print("=" * 60)
    
    # 项目路径配置
    current_dir = os.path.dirname(os.path.abspath(__file__))
    bin_dir = os.path.join(current_dir, 'deploy', 'bin')
    config_path = os.path.join(current_dir, 'deploy', 'config')
    logs_path = os.path.join(current_dir, 'deploy', 'logs')
    
    # 确保日志目录存在
    os.makedirs(logs_path, exist_ok=True)
    
    # 服务列表
    services = [
        ('HT000001', 'HTTP服务器', 'http://127.0.0.1:9000/'),
        ('CO000001', '连接服务器', 'ws://127.0.0.1:9200/'),
        ('US000001', '用户服务器', None),
        ('RM000001', '房间服务器', None)
    ]
    
    # 设置环境变量
    env = dict(os.environ)
    env["PYTHONPATH"] = f"{current_dir};{bin_dir}"
    
    processes = []
    
    def signal_handler(sig, frame):
        print("\n\n🛑 收到停止信号，正在关闭所有服务...")
        for service_id, service_name, process, _ in processes:
            if process and process.poll() is None:
                print(f"   停止 {service_name}...")
                process.terminate()
        print("✅ 所有服务已停止")
        sys.exit(0)
    
    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    
    print("开始启动服务...")
    print("-" * 40)
    
    for service_id, service_name, url in services:
        print(f"\n🔄 启动 {service_name} ({service_id})...")
        
        cmd = [
            sys.executable,
            'debug_start_service.py',
            service_id
        ]
        
        try:
            # 在后台启动服务
            process = subprocess.Popen(
                cmd,
                env=env,
                cwd=current_dir,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                bufsize=1
            )
            
            processes.append((service_id, service_name, process, url))
            
            # 等待服务启动
            print(f"   ⏳ 等待 {service_name} 启动...")
            time.sleep(3)
            
            if process.poll() is not None:
                print(f"   ❌ {service_name} 启动失败")
                # 读取错误输出
                output, _ = process.communicate()
                if output:
                    print(f"   错误信息: {output[-200:]}")  # 只显示最后200字符
            else:
                print(f"   ✅ {service_name} 启动成功 (PID: {process.pid})")
            
        except Exception as e:
            print(f"   ❌ {service_name} 启动失败: {e}")
    
    # 检查哪些服务还在运行
    running_services = []
    for service_id, service_name, process, url in processes:
        if process and process.poll() is None:
            running_services.append((service_id, service_name, process, url))
    
    if running_services:
        print("\n" + "=" * 60)
        print("🎉 服务启动完成！")
        print("=" * 60)
        
        print("\n📊 运行中的服务:")
        for service_id, service_name, process, url in running_services:
            if url:
                print(f"   ✅ {service_name}: {url} (PID: {process.pid})")
            else:
                print(f"   ✅ {service_name}: 运行中 (PID: {process.pid})")
        
        print(f"\n📁 日志目录: {logs_path}")
        print("💡 按 Ctrl+C 停止所有服务")
        print("=" * 60)
        
        # 监控服务状态
        def monitor_services():
            while True:
                time.sleep(10)  # 每10秒检查一次
                for service_id, service_name, process, url in running_services[:]:
                    if process.poll() is not None:
                        print(f"\n⚠️  {service_name} 已停止运行")
                        running_services.remove((service_id, service_name, process, url))
                
                if not running_services:
                    print("\n❌ 所有服务都已停止")
                    break
        
        # 启动监控线程
        monitor_thread = threading.Thread(target=monitor_services, daemon=True)
        monitor_thread.start()
        
        try:
            # 等待所有进程结束
            while running_services:
                time.sleep(1)
        except KeyboardInterrupt:
            signal_handler(signal.SIGINT, None)
            
    else:
        print("\n❌ 没有服务成功启动")
        print("请检查:")
        print("1. Redis是否运行")
        print("2. 端口是否被占用")
        print("3. 日志文件获取详细错误信息")
    
    print("\n👋 服务管理器已退出")

if __name__ == '__main__':
    main()
